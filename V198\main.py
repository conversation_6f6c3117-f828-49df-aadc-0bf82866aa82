import json
import re
import sqlite3
import time
import tkinter as tk
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from tkinter import filedialog, messagebox, ttk

import numpy as np
import pypinyin
import screeninfo
import windnd
from cachetools import LRUCache
from database_manager import DatabaseManager
from keytime import AutoPlayer, KeyTimeRecorder
from PIL import Image, ImageTk
from yuantu import YuanTuManager

# 数据库优化
# 新增自定义排序功能，并且可以重置

try:
    import keyboard

    KEYBOARD_AVAILABLE = True
except ImportError:
    KEYBOARD_AVAILABLE = False

# 全局变量用于存储应用实例
app_instance = None


class ImageProjector:
    def __init__(self, root):
        """初始化应用"""
        self.root = root
        self.root.title("歌谱投影控制器V1.9.8")

        # 设置默认字体
        self.default_font = "Microsoft YaHei UI"

        # 修改：使用当前运行目录作为基准
        current_dir = Path.cwd()
        self.db_path = current_dir / "pyimages.db"
        print(f"数据库路径: {self.db_path}")

        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 配置文件也直接放在当前目录中
        self.config_file = current_dir / "config.json"

        # 初始化数据库管理器
        self.db_manager = DatabaseManager(str(self.db_path), enable_optimizations=True)

        # 初始化数据库 - 只需要一次
        self.init_database()

        # 初始化时间录制和自动播放模块
        self.init_keytime_modules()

        # 初始化DPI缩放
        self.init_dpi_scaling()

        # 加载字体设置 - 在创建UI元素前加载
        self.load_font_settings()

        # 初始化搜索组件样式
        self.init_search_styles()

        # 应用字体设置到所有UI元素 - 在UI创建后应用
        self.root.after(100, self.update_all_fonts)

        # 添加缓存大小限制
        self.max_cache_size = 100  # 设置最大缓存数量

        self.thread_pool = ThreadPoolExecutor(max_workers=4)

        # 初始化缓存
        self.image_cache = ImageCache()

        # 初始化默认值
        self.zoom_ratio = 1.0
        self.zoom_step = 1.1

        # 首先定义黄字颜色预设
        self.yellow_text_presets = {
            "默认": {"r": 174, "g": 159, "b": 112},
            "纯黄": {"r": 255, "g": 255, "b": 0},
            "秋麒麟": {"r": 218, "g": 165, "b": 32},
            "晒黑": {"r": 210, "g": 180, "b": 140},
            "结实的树": {"r": 222, "g": 184, "b": 135},
            "马鞍棕色": {"r": 139, "g": 69, "b": 19},
            "沙棕色": {"r": 244, "g": 164, "b": 96},
        }

        # 初始化当前黄字颜色名称（将在加载配置时设置实际值）
        self.current_yellow_color_name = None
        self.yellow_text_rgb = None

        # 初始化图片相关变量
        self.image = None
        self.photo = None
        self.image_on_canvas = None
        self.image_items = []  # 初始化项目列表

        # 初始化屏幕信息
        try:
            self.screens = screeninfo.get_monitors()
            if not self.screens:
                self.screens = [
                    screeninfo.Monitor(
                        x=0,
                        y=0,
                        width=root.winfo_screenwidth(),
                        height=root.winfo_screenheight(),
                    )
                ]
        except Exception as e:
            print(f"获取屏幕信息失败: {e}")
            self.screens = [
                screeninfo.Monitor(
                    x=0,
                    y=0,
                    width=root.winfo_screenwidth(),
                    height=root.winfo_screenheight(),
                )
            ]

        # 创建顶部菜单栏Frame
        screen_width = root.winfo_screenwidth()
        menu_height = int(screen_width / 20)
        self.menu_frame = tk.Frame(root, bg="#f0f0f0", height=menu_height)
        self.menu_frame.pack(fill=tk.X, side=tk.TOP)
        self.menu_frame.pack_propagate(False)

        # 创建macOS风格按钮
        self.create_menu()

        # 主框架
        self.main_frame = ttk.Frame(root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建左右分栏
        self.paned = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.paned.pack(fill=tk.BOTH, expand=True)

        # 左侧项目列表框架 - 添加最小宽度和初始宽度
        self.left_frame = ttk.Frame(self.paned, width=250)  # 设置初始宽度
        self.left_frame.pack_propagate(False)  # 防止子组件影响框架大小
        self.paned.add(self.left_frame)  # 移除 minsize 参数

        # 右侧主内容框架
        self.right_frame = ttk.Frame(self.paned)
        self.paned.add(self.right_frame, weight=1)

        # 在窗口显示后设置分隔线位置
        self.root.after(100, self.set_initial_pane_position)

        # 设置左侧面板的初始宽度（在添加完所有组件后设置）
        self.root.update_idletasks()  # 确保组件已经完成初始布局

        # 创建项目列表（在加载配置之前）
        self.create_project_list()

        # 初始化后立即加载数据库内容
        self.load_projects()

        self.load_config()

        # 如果配置文件不存在，自动生成默认配置文件
        if not self.config_file.exists():
            self.save_config()

        # 创投屏窗口
        self.second_window = None
        self.second_canvas = None

        # 初始化滚动条和画布
        self.scrollbar = tk.Scrollbar(
            self.right_frame,
            orient=tk.VERTICAL,
            width=60,
            command=self.on_scroll,
            bg="#8B00FF",  # 紫色背景
            activebackground="#9932CC",  # 鼠标悬停时的颜色
            troughcolor="#4B0082",  # 滚动槽的颜色（深紫色）
        )
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置画布和滚动条的关联
        self.canvas = tk.Canvas(self.right_frame, bg="black")
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # 定鼠标滚轮事件
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)

        # 添加 windnd 拖拽支持
        try:
            windnd.hook_dropfiles(root, func=self.handle_drop)
        except Exception as e:
            print(f"拖放初始化错误: {e}")

        # 初始化其他变量
        self.sync_enabled = False
        self.last_scroll_time = 0
        self.scroll_update_delay = 16  # 固定延迟
        self.is_scrolling = False
        self.scroll_timer = None

        # 添加反色状态标志
        self.is_inverted = False

        # 添加普通反色状态标志
        self.is_simple_inverted = False

        # 添加原图模式标志
        self.original_mode = False

        # 移除图片效果缓存，改为完全实时处理

        # 添加关键帧相关的状态变量
        self.current_image_id = None
        self.current_keyframe_index = -1
        self.keyframe_markers = []

        # 绑定窗口大小改变事件
        self.root.bind("<Configure>", self.on_window_resize)

        # 保留左右方向键绑定用于在原图模式下切换图片（窗口内使用）
        self.root.bind("<Left>", lambda e: self.handle_arrow_key("prev"))  # 左键
        self.root.bind("<Right>", lambda e: self.handle_arrow_key("next"))  # 右键

        # PageUp/PageDown现在通过全局热键处理，移除窗口内绑定避免重复触发

        # 创建关键帧指示器
        self.create_keyframe_indicator()

        # 创建预览线
        self.preview_line = None
        self.next_keyframe_line = None
        self.current_scroll_position = 0
        self.create_preview_lines()

        # 创建联系方式按钮
        self.btn_contact = tk.Button(
            self.menu_frame, text="作者", command=self.show_contact_info, **button_style
        )
        self.btn_contact.pack(side=tk.LEFT, padx=button_spacing)

        # 创建画布右键菜单
        self.create_canvas_context_menu()

        # 绑定画布右键点击事件
        self.canvas.bind("<Button-3>", self.show_canvas_context_menu)

        # 加载滚动速度设置
        self.load_scroll_speed_settings()

        # 启动时自动同步文件夹内容（使用优化版本）
        self.root.after(1000, self.quick_sync_check)

        # 设置全局应用实例
        global app_instance
        app_instance = self

        # 初始化全局热键状态标志
        self.global_hotkeys_enabled = False

        # 移除自动设置全局热键，只在投影开启时启用
        # self.setup_global_hotkeys()

    def safe_db_execute(self, query, params=None, fetch=False, commit=True):
        """统一的数据库操作方法，减少重复代码（向后兼容）"""
        return self.db_manager.safe_execute(query, params, fetch, commit)

    def execute_batch_operations(self, operations):
        """批量执行数据库操作的便利方法"""
        return self.db_manager.execute_batch(operations)

    def execute_many_operations(self, query, params_list):
        """执行多个相同查询的便利方法"""
        return self.db_manager.execute_many(query, params_list)

    def get_database_connection(self):
        """获取数据库连接的便利方法"""
        return self.db_manager.get_connection()

    def get_database_transaction(self):
        """获取数据库事务的便利方法"""
        return self.db_manager.transaction()

    def show_database_stats(self):
        """显示数据库性能统计信息"""
        try:
            stats = self.db_manager.get_database_stats()

            stats_text = f"""数据库性能统计信息：

数据库大小: {stats.get('database_size_mb', 'N/A')} MB
表数量: {stats.get('table_count', 'N/A')}
索引数量: {stats.get('index_count', 'N/A')}
日志模式: {stats.get('journal_mode', 'N/A')}

优化状态: {'已启用' if self.db_manager.enable_optimizations else '未启用'}
连接状态: {'已连接' if self.db_manager._conn else '未连接'}
"""

            messagebox.showinfo("数据库统计", stats_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取数据库统计信息失败: {e}")

    def optimize_database_manually(self):
        """手动优化数据库"""
        try:
            self.db_manager.optimize_database()
            messagebox.showinfo("成功", "数据库优化完成")
        except Exception as e:
            messagebox.showerror("错误", f"数据库优化失败: {e}")

    def init_database(self):
        """初始化数据库（优化版本：使用数据库管理器）"""
        try:
            with self.db_manager.get_connection() as conn:
                # 添加新表用于存储UI配置
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS ui_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL
                    )
                """
                )

                # 添加通用设置表
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS settings (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL
                    )
                """
                )

                # 首先创建所有表
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS folders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        path TEXT UNIQUE NOT NULL,
                        order_index INTEGER,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS images (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        path TEXT UNIQUE NOT NULL,
                        folder_id INTEGER,
                        last_modified TIMESTAMP,
                        order_index INTEGER,
                        FOREIGN KEY (folder_id) REFERENCES folders(id),
                        UNIQUE(path)
                    )
                """
                )

                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS keyframes (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        image_id INTEGER NOT NULL,
                        position REAL NOT NULL,
                        y_position INTEGER NOT NULL,
                        order_index INTEGER,
                        FOREIGN KEY (image_id) REFERENCES images(id)
                    )
                """
                )

                # 添加原图标记表
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS original_marks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        item_type TEXT NOT NULL CHECK (item_type IN ('folder', 'image')),
                        item_id INTEGER NOT NULL,
                        mark_type TEXT NOT NULL DEFAULT 'loop' CHECK (mark_type IN ('loop', 'sequence')),
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(item_type, item_id)
                    )
                """
                )

                # 检查并添加mark_type列（如果不存在）
                try:
                    conn.execute(
                        'ALTER TABLE original_marks ADD COLUMN mark_type TEXT DEFAULT "loop" CHECK (mark_type IN ("loop", "sequence"))'
                    )
                except sqlite3.OperationalError:
                    # 列已存在，忽略错误
                    pass

                # 添加手动排序标记表
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS manual_sort_folders (
                        folder_id INTEGER PRIMARY KEY,
                        is_manual_sort BOOLEAN DEFAULT 0,
                        last_manual_sort_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE
                    )
                """
                )

                # 添加图片显示位置表（支持同一图片在多个位置显示）
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS image_display_locations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        image_id INTEGER NOT NULL,
                        location_type TEXT NOT NULL CHECK (location_type IN ('folder', 'root')),
                        folder_id INTEGER,
                        order_index INTEGER,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
                        FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE,
                        UNIQUE(image_id, location_type, folder_id)
                    )
                """
                )

                # 然后创建所有索引
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_folder_id ON images(folder_id)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_order_images ON images(order_index)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_order_folders ON folders(order_index)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_keyframes_image ON keyframes(image_id)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_keyframes_order ON keyframes(order_index)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_original_marks ON original_marks(item_type, item_id)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_images_name ON images(name)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_images_folder_order ON images(folder_id, order_index)"
                )
                conn.execute(
                    "CREATE INDEX IF NOT EXISTS idx_manual_sort ON manual_sort_folders(folder_id)"
                )

                conn.commit()
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            raise e

    def init_keytime_modules(self):
        """初始化时间录制和自动播放模块"""
        try:
            # 初始化时间录制器
            self.time_recorder = KeyTimeRecorder(str(self.db_path))

            # 初始化自动播放器
            self.auto_player = AutoPlayer(self, self.time_recorder)

            # 设置播放结束回调函数
            self.auto_player.on_play_finished = self._on_auto_play_finished

            # 初始化相关状态变量
            self.is_recording_timing = False
            self.is_auto_playing = False

            # 播放次数设定 - 自定义输入
            self.target_play_count = 5  # 默认5次
            self.load_play_count_setting()  # 加载保存的设置

            # print("时间录制和自动播放模块初始化完成")

        except Exception as e:
            print(f"初始化keytime模块失败: {e}")
            # 创建空的替代对象，避免程序崩溃
            self.time_recorder = None
            self.auto_player = None
            self.is_recording_timing = False
            self.is_auto_playing = False

    def add_keyframe(self, image_id, position):
        """添加关键帧 - 仅标记位置,不执行任何操作"""
        try:
            # 直接获取当前可见区域顶部的y坐标
            current_y = self.canvas.canvasy(0)

            with sqlite3.connect(self.db_path) as conn:
                # 检查是否已存在相近位置的关键帧
                cursor = conn.execute(
                    """
                    SELECT y_position
                    FROM keyframes
                    WHERE image_id = ? AND ABS(y_position - ?) < 50
                """,
                    (image_id, current_y),
                )

                if cursor.fetchone():
                    print("该位置附近已存在关键帧")
                    return False

                # 获取当前最大order_index
                cursor = conn.execute(
                    """
                    SELECT COALESCE(MAX(order_index), -1)
                    FROM keyframes
                    WHERE image_id = ?
                """,
                    (image_id,),
                )
                max_order = cursor.fetchone()[0]
                next_order = max_order + 1

                # 插入新关键帧
                conn.execute(
                    """
                    INSERT INTO keyframes (image_id, position, y_position, order_index)
                    VALUES (?, ?, ?, ?)
                """,
                    (image_id, position, int(current_y), next_order),
                )

                conn.commit()

                # 仅更新预览线显示,不执行滚动
                self.update_preview_lines()
                return True

        except Exception as e:
            print(f"添加关键帧失败: {e}")
            return False

    def get_keyframes(self, image_id):
        """获取指定图片的所有关键帧"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT id, position, order_index
                    FROM keyframes
                    WHERE image_id = ?
                    ORDER BY order_index
                """,
                    (image_id,),
                )
                return cursor.fetchall()
        except Exception as e:
            print(f"获取关键帧失败: {e}")
            return []

    def delete_keyframe(self, keyframe_id):
        """删除单个关键帧"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 获取关键帧信息用于重排序
                cursor = conn.execute(
                    """
                    SELECT image_id, order_index
                    FROM keyframes
                    WHERE id = ?
                """,
                    (keyframe_id,),
                )
                result = cursor.fetchone()
                if not result:
                    return False

                image_id, order_index = result

                # 删除关键帧
                conn.execute("DELETE FROM keyframes WHERE id = ?", (keyframe_id,))

                # 更新后续关键帧的顺序
                conn.execute(
                    """
                    UPDATE keyframes
                    SET order_index = order_index - 1
                    WHERE image_id = ? AND order_index > ?
                """,
                    (image_id, order_index),
                )

                conn.commit()
                print(f"删除关键帧成功: id={keyframe_id}")
                return True
        except Exception as e:
            print(f"删除关键帧失败: {e}")
            return False

    def clear_keyframes(self, image_id):
        """清除指定图片的所有关键帧"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM keyframes WHERE image_id = ?", (image_id,))
                conn.commit()
            return True
        except Exception as e:
            print(f"清除关键帧失败: {e}")
            return False

    def clear_all_keyframes(self):
        """清除所有关键帧(谨慎使用)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM keyframes")
                conn.commit()
            return True
        except Exception as e:
            print(f"清除所有关键帧失败: {e}")
            return False

    def update_keyframe_order(self, image_id):
        """重新整理关键帧顺序"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 获取所有关键帧并按position排序
                cursor = conn.execute(
                    """
                    SELECT id
                    FROM keyframes
                    WHERE image_id = ?
                    ORDER BY position
                """,
                    (image_id,),
                )

                # 更新order_index
                for index, (keyframe_id,) in enumerate(cursor.fetchall()):
                    conn.execute(
                        """
                        UPDATE keyframes
                        SET order_index = ?
                        WHERE id = ?
                    """,
                        (index, keyframe_id),
                    )

                conn.commit()
                print(f"更新关键帧顺序成功: image_id={image_id}")
                return True
        except Exception as e:
            print(f"更新关键帧顺序失败: {e}")
            return False

    def create_menu(self):
        # 进一步减小 padding 值
        padding_x = int(self.font_size * 0.3)
        padding_y = int(self.font_size * 0.2)

        # 定义按钮样式
        global button_style, button_spacing
        button_style = {
            "relie": "flat",
            "padx": padding_x,
            "pady": padding_y,
            "font": (self.default_font, self.menu_font_size),  # 使用统一的菜单字体大小
            "cursor": "hand2",
            "bg": "#f0f0f0",
        }

        button_spacing = int(self.font_size / 6)

        self.import_menu = tk.Menu(self.root, tearoff=0)
        self.import_menu.add_command(
            label="导入单个图片",
            command=self.import_single_image,
            font=(self.default_font, self.menu_font_size),  # 使用统一的菜单字体大小
        )
        self.import_menu.add_command(
            label="导入文件夹",
            command=self.import_folder,
            font=(self.default_font, self.menu_font_size),  # 使用统一的菜单字体大小
        )
        # 添加分隔线
        self.import_menu.add_separator()
        # 添加另存黄字图片选项
        self.import_menu.add_command(
            label="另存黄字图片",
            command=self.save_yellow_text_image,
            font=(self.default_font, self.menu_font_size),  # 使用统一的菜单字体大小
        )

        # 设置下拉框样式
        style = ttk.Style()
        style.configure(
            "TCombobox", padding=5, font=(self.default_font, self.menu_font_size)
        )  # 使用统一的菜单字体大小

        # 设置下拉列表样式
        self.root.option_add(
            "*TCombobox*Listbox.font", (self.default_font, self.menu_font_size)
        )  # 使用统一的菜单字体大小

        self.btn_import = tk.Button(
            self.menu_frame, text="导入", command=self.show_import_menu, **button_style
        )
        self.btn_show = tk.Button(
            self.menu_frame, text="投影", command=self.toggle_projection, **button_style
        )
        self.btn_sync = tk.Button(
            self.menu_frame, text="同步", command=self.sync_all_folders, **button_style
        )
        # 删除更新按钮
        self.btn_top = tk.Button(
            self.menu_frame, text="返回", command=self.reset_view, **button_style
        )
        self.btn_original = tk.Button(
            self.menu_frame,
            text="原图",
            command=self.toggle_original_mode,
            **button_style,
        )

        # 创建缩放菜单
        self.zoom_menu = tk.Menu(self.root, tearoff=0)
        self.zoom_menu.add_command(
            label="还原",
            command=self.zoom_reset_wrapper,
            font=(self.default_font, self.menu_font_size),
        )
        self.zoom_menu.add_command(
            label="放大",
            command=self.zoom_in_wrapper,
            font=(self.default_font, self.menu_font_size),
        )
        self.zoom_menu.add_command(
            label="缩小",
            command=self.zoom_out_wrapper,
            font=(self.default_font, self.menu_font_size),
        )

        # 创建缩放按钮
        self.btn_zoom = tk.Button(
            self.menu_frame, text="缩放", command=self.show_zoom_menu, **button_style
        )

        self.btn_invert = tk.Button(
            self.menu_frame, text="变色", command=self.toggle_invert, **button_style
        )
        self.btn_simple_invert = tk.Button(
            self.menu_frame,
            text="反色",
            command=self.toggle_simple_invert,
            **button_style,
        )

        # 修改按钮布局
        self.btn_import.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_show.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_sync.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_top.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_original.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_zoom.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_invert.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_simple_invert.pack(side=tk.LEFT, padx=button_spacing)

        # 添加屏幕选择下拉框
        ttk.Label(
            self.menu_frame,
            text="屏幕:",
            font=("Microsoft YaHei UI", int(self.font_size * 0.6)),
        ).pack(
            side=tk.LEFT, padx=5
        )  # 缩小标签字体

        self.screen_var = tk.StringVar()
        self.screen_combo = ttk.Combobox(
            self.menu_frame,
            textvariable=self.screen_var,
            width=3,
            font=("Microsoft YaHei UI", int(self.font_size * 0.6)),
            state="readonly",
        )
        self.screen_combo.pack(side=tk.LEFT, padx=2)

        # 设置下拉框样式
        style = ttk.Style()
        style.configure(
            "TCombobox",
            padding=5,
            font=("Microsoft YaHei UI", int(self.font_size * 0.6)),
        )  # 设置字体大小

        # 设置下拉列表样式
        self.root.option_add(
            "*TCombobox*Listbox.font", ("Microsoft YaHei UI", int(self.font_size * 0.6))
        )  # 设置下拉表字体
        self.root.option_add(
            "*TCombobox*Listbox.selectBackground", "#0078D7"
        )  # 设置选中背景色
        self.root.option_add(
            "*TCombobox*Listbox.selectForeground", "white"
        )  # 设置选文字颜色

        self.update_screen_list()

        # 绑定悬停效果
        for btn in (
            self.btn_import,
            self.btn_show,
            self.btn_sync,
            self.btn_top,
            self.btn_original,
            self.btn_zoom,
            self.btn_invert,
            self.btn_simple_invert,
        ):
            btn.bind("<Enter>", lambda e, b=btn: self.on_hover(b, True))
            btn.bind("<Leave>", lambda e, b=btn: self.on_hover(b, False))

        # 添加关键帧控制按钮组
        self.create_keyframe_controls()

        # 添加字体大小调整按钮
        self.font_menu = tk.Menu(self.root, tearoff=0)
        font_sizes = [18, 20, 22, 30, 35, 40]
        for size in font_sizes:
            self.font_menu.add_command(
                label=f"{size}号",
                command=lambda s=size: self.set_font_size(s),
                font=(self.default_font, self.menu_font_size),
            )

        # 添加字体大小调整按钮
        self.btn_font = tk.Button(
            self.menu_frame, text="字号", command=self.show_font_menu, **button_style
        )
        self.btn_font.pack(side=tk.LEFT, padx=button_spacing)
        self.btn_font.bind("<Enter>", lambda e, b=self.btn_font: self.on_hover(b, True))
        self.btn_font.bind(
            "<Leave>", lambda e, b=self.btn_font: self.on_hover(b, False)
        )

    def create_keyframe_controls(self):
        """创建关键帧控制按钮组"""
        # 创建一个Frame来容纳关键帧控制按钮
        self.keyframe_frame = tk.Frame(self.menu_frame, bg="#f0f0f0")
        self.keyframe_frame.pack(side=tk.LEFT, padx=10)

        # 设置按钮样式
        button_style = {
            "relie": "flat",
            "padx": int(self.font_size * 0.3),
            "pady": int(self.font_size * 0.2),
            "font": ("Microsoft YaHei UI", int(self.font_size * 0.6)),
            "cursor": "hand2",
        }

        self.btn_add_keyframe = tk.Button(
            self.keyframe_frame,
            text="加帧",
            command=self.add_current_keyframe,
            **button_style,
        )

        # 清除关键帧按钮
        self.btn_clear_keyframes = tk.Button(
            self.keyframe_frame,
            text="清帧",
            command=self.clear_current_keyframes,
            **button_style,
        )

        # 上一个关键帧按钮
        self.btn_prev = tk.Button(
            self.keyframe_frame,
            text="上帧",
            command=self.handle_pageup_key,
            **button_style,
        )

        # 下一个关键帧按钮
        self.btn_next = tk.Button(
            self.keyframe_frame,
            text="下帧",
            command=self.handle_pagedown_key,
            **button_style,
        )

        # 播放次数按钮
        self.btn_loop = tk.Button(
            self.keyframe_frame, text="1次", command=self.set_play_count, **button_style
        )

        # 时间录制按钮
        self.btn_record = tk.Button(
            self.keyframe_frame,
            text="录制",
            command=self.toggle_timing_recording,
            bg="#ffcccc",
            **button_style,
        )

        # 自动播放按钮
        self.btn_play = tk.Button(
            self.keyframe_frame,
            text="播放",
            command=self.toggle_auto_play,
            **button_style,
        )

        # 清除时间按钮
        self.btn_clear_timing = tk.Button(
            self.keyframe_frame,
            text="清时",
            command=self.clear_timing_data,
            **button_style,
        )

        # 脚本信息按钮
        self.btn_script_info = tk.Button(
            self.keyframe_frame,
            text="脚本",
            command=self.show_script_info,
            **button_style,
        )

        # 打包按钮 - 调整间距和顺序
        self.btn_add_keyframe.pack(side=tk.LEFT, padx=3)
        self.btn_clear_keyframes.pack(side=tk.LEFT, padx=3)

        # 添加分隔间距
        separator1 = tk.Frame(self.keyframe_frame, width=8, bg="#f0f0f0")
        separator1.pack(side=tk.LEFT)

        self.btn_prev.pack(side=tk.LEFT, padx=3)
        self.btn_next.pack(side=tk.LEFT, padx=3)

        # 添加分隔间距
        separator2 = tk.Frame(self.keyframe_frame, width=8, bg="#f0f0f0")
        separator2.pack(side=tk.LEFT)

        # 播放、次数、录制按钮顺序
        self.btn_play.pack(side=tk.LEFT, padx=3)
        self.btn_loop.pack(side=tk.LEFT, padx=3)
        self.btn_record.pack(side=tk.LEFT, padx=3)

        # 添加分隔间距
        separator3 = tk.Frame(self.keyframe_frame, width=8, bg="#f0f0f0")
        separator3.pack(side=tk.LEFT)

        self.btn_script_info.pack(side=tk.LEFT, padx=3)
        self.btn_clear_timing.pack(side=tk.LEFT, padx=3)

        # 初始化状态
        self.current_keyframe_index = -1
        self.keyframe_markers = []
        # 默认开启循环模式
        self.is_loop_enabled = True

        # 初始化滚动速度设置
        self.scroll_duration = 0.0  # 默认滚动时间为0秒（立即跳转）

        # 操作历史记录（用于智能跳转判断）
        self.keyframe_operation_history = []
        self.last_operation_time = 0
        self.auto_jump_delay = 2.0  # 2秒无操作后自动跳转

        # 初始化原图模式管理器
        self.yuantu_manager = YuanTuManager(self)

        # 更新播放次数按钮显示以反映加载的设置
        self._update_play_count_button()

    def step_to_prev_keyframe(self):
        """执行前一个关键帧"""
        # 移除原图模式下切换图片的逻辑，恢复关键帧功能
        if not hasattr(self, "current_image_id"):
            return

        keyframes = self.get_keyframes(self.current_image_id)
        if not keyframes:
            return

        # 播放时间修正逻辑：如果正在播放，记录手动操作进行时间修正
        if (
            self.is_auto_playing
            and self.auto_player
            and self.current_keyframe_index >= 0
        ):
            current_keyframe_id = keyframes[self.current_keyframe_index][0]
            self.auto_player.record_manual_operation(current_keyframe_id)

        # 时间录制逻辑：记录当前关键帧的停留时间（在切换前记录）
        if (
            self.is_recording_timing
            and self.time_recorder
            and self.current_keyframe_index >= 0
        ):
            # 获取当前关键帧的ID
            current_keyframe_id = keyframes[self.current_keyframe_index][0]
            self.time_recorder.record_keyframe_timing(current_keyframe_id)
            # print(f"记录关键帧 {self.current_keyframe_index + 1} (ID:{current_keyframe_id}) 的停留时间（向前切换）")

        # 记录操作历史
        import time

        current_time = time.time()
        current_index = self.current_keyframe_index
        self.keyframe_operation_history.append(
            {"type": "prev", "from_index": current_index, "time": current_time}
        )
        self.last_operation_time = current_time

        # 取消任何待定的跳转检查
        if hasattr(self, "_jump_check_timer"):
            self.root.after_cancel(self._jump_check_timer)

        # 更新当前帧索引
        self.current_keyframe_index -= 1
        if self.current_keyframe_index < 0:
            # 直接跳转到最后一帧
            self.current_keyframe_index = len(keyframes) - 1
            _, target_position, _ = keyframes[self.current_keyframe_index]

            # 直接跳转到最后一帧
            self.canvas.yview_moveto(target_position)
            if self.second_window and self.sync_enabled:
                self.sync_projection_screen_absolute()

            # 更新指示器和预览线
            self.update_keyframe_indicators()
            self.update_preview_lines()

            # 确保投影窗口也更新
            if self.second_window and self.sync_enabled:
                self.update_projection()

            print("已到达第一帧，直接跳转到最后一帧")
            return

        # 获取目标位置
        _, target_position, _ = keyframes[self.current_keyframe_index]

        # 检查滚动时间，如果为0则直接跳转，否则平滑滚动
        if self.scroll_duration == 0:
            # 取消任何正在进行的滚动动画
            if hasattr(self, "_scroll_animation_id"):
                self.root.after_cancel(self._scroll_animation_id)

            # 直接设置位置
            self.canvas.yview_moveto(target_position)

            # 同步投影
            if self.second_window and self.sync_enabled:
                self.sync_projection_screen_absolute()

            # 更新预览线（0秒跳转时需要手动更新）
            self.update_preview_lines()
        else:
            # 平滑滚动到该位置
            self.smooth_scroll_to(target_position)

        # 更新指示器
        self.update_keyframe_indicators()

        # 确保投影窗口也更新
        if self.second_window and self.sync_enabled:
            self.update_projection()

    def step_to_next_keyframe(self):
        """执行下一个关键帧"""
        # 移除原图模式下切换图片的逻辑，恢复关键帧功能
        if not hasattr(self, "current_image_id"):
            return

        keyframes = self.get_keyframes(self.current_image_id)
        if not keyframes:
            return

        # 播放时间修正逻辑：如果正在播放，记录手动操作进行时间修正
        if (
            self.is_auto_playing
            and self.auto_player
            and self.current_keyframe_index >= 0
        ):
            current_keyframe_id = keyframes[self.current_keyframe_index][0]
            self.auto_player.record_manual_operation(current_keyframe_id)

        # 时间录制逻辑：记录当前关键帧的停留时间
        if (
            self.is_recording_timing
            and self.time_recorder
            and self.current_keyframe_index >= 0
        ):
            # 获取当前关键帧的ID
            current_keyframe_id = keyframes[self.current_keyframe_index][0]
            self.time_recorder.record_keyframe_timing(current_keyframe_id)
            # print(f"记录关键帧 {self.current_keyframe_index + 1} (ID:{current_keyframe_id}) 的停留时间（向后切换）")

        # 获取当前关键帧索引
        current_index = self.current_keyframe_index

        # 记录操作历史
        import time

        current_time = time.time()
        self.keyframe_operation_history.append(
            {"type": "next", "from_index": current_index, "time": current_time}
        )
        self.last_operation_time = current_time

        # 取消任何待定的跳转检查（用户还在操作）
        if hasattr(self, "_jump_check_timer"):
            self.root.after_cancel(self._jump_check_timer)

        # 更新当前帧索引
        self.current_keyframe_index += 1

        # 检查是否超出范围
        if self.current_keyframe_index >= len(keyframes):
            # 已经超出范围，直接跳转到第一帧
            self.current_keyframe_index = 0
            _, target_position, _ = keyframes[self.current_keyframe_index]

            # 新增：如果正在录制，自动停止录制
            if self.is_recording_timing:
                self.toggle_timing_recording()

            # 直接跳转到第一帧
            self.canvas.yview_moveto(target_position)
            if self.second_window and self.sync_enabled:
                self.sync_projection_screen_absolute()

            # 更新指示器和预览线
            self.update_keyframe_indicators()
            self.update_preview_lines()

            # 确保投影窗口也更新
            if self.second_window and self.sync_enabled:
                self.update_projection()

            # print("已到达最后一帧，直接跳转到第一帧")
            return
        else:
            is_looping_back = False

        # 检查是否是首次执行（之前未播放过关键帧）
        is_first_execution = current_index == -1

        # 如果是循环回第一帧或者是首次执行，使用直接跳转
        if is_looping_back or is_first_execution:
            # 设置到第一帧 - 使用直接跳转
            self.current_keyframe_index = 0
            _, target_position, _ = keyframes[self.current_keyframe_index]

            # 直接设置位置而不是平滑滚动
            self.canvas.yview_moveto(target_position)
            if self.second_window and self.sync_enabled:
                self.sync_projection_screen_absolute()
        else:
            # 正常情况下滚动到下一帧
            _, target_position, _ = keyframes[self.current_keyframe_index]

            # 检查滚动时间，如果为0则直接跳转，否则平滑滚动
            if self.scroll_duration == 0:
                # 取消任何正在进行的滚动动画
                if hasattr(self, "_scroll_animation_id"):
                    self.root.after_cancel(self._scroll_animation_id)

                # 直接设置位置
                self.canvas.yview_moveto(target_position)

                # 同步投影
                if self.second_window and self.sync_enabled:
                    self.sync_projection_screen_absolute()

                # 更新预览线（0秒跳转时需要手动更新）
                self.update_preview_lines()
            else:
                # 平滑滚动到该位置
                self.smooth_scroll_to(target_position)

        # 更新指示器和预览线
        self.update_keyframe_indicators()
        self.update_preview_lines()

        # 确保投影窗口也更新
        if self.second_window and self.sync_enabled:
            self.update_projection()

    def _should_auto_jump_to_first(self):
        """判断是否应该智能跳转到第一帧"""
        if not hasattr(self, "current_image_id"):
            return False

        # 如果没有时间录制器，使用简单的跳转逻辑
        if not self.time_recorder:
            return True  # 允许跳转

        # 检查是否有录制的时间数据
        if not self.time_recorder.has_timing_data(self.current_image_id):
            return True  # 没有录制数据时也允许跳转

        # 分析操作历史，判断用户是否完成了一个完整的浏览周期
        if len(self.keyframe_operation_history) < 3:  # 至少需要几个操作
            return False

        # 检查最近的操作模式
        recent_ops = (
            self.keyframe_operation_history[-4:]
            if len(self.keyframe_operation_history) >= 4
            else self.keyframe_operation_history[-3:]
        )

        # 统计最近操作中的向前和向后次数
        forward_count = sum(1 for op in recent_ops if op["type"] == "next")
        backward_count = sum(1 for op in recent_ops if op["type"] == "prev")

        # 如果有明显的来回切换模式，不跳转
        if backward_count > 0 and forward_count > 0:
            print(
                f"检测到来回切换模式 (前进:{forward_count}, 后退:{backward_count})，不跳转"
            )
            return False

        # 如果最近主要是连续向前操作，说明用户在顺序浏览，可以自动跳转
        jump_decision = forward_count >= 3 and backward_count == 0
        print(
            f"跳转判断: 前进{forward_count}次, 后退{backward_count}次 -> {'跳转' if jump_decision else '不跳转'}"
        )
        return jump_decision

    def _should_auto_jump_to_last(self):
        """判断是否应该智能跳转到最后一帧"""
        # 向前跳转的条件更严格，需要用户明确的反向浏览意图
        if len(self.keyframe_operation_history) < 2:
            return False

        # 检查最近的操作是否是连续向前的
        recent_ops = self.keyframe_operation_history[-2:]
        backward_count = sum(1 for op in recent_ops if op["type"] == "prev")

        # 如果最近都是向前操作，可以跳转
        return backward_count >= 2

    def _schedule_jump_check(self):
        """安排延迟跳转检查"""
        # 取消之前的跳转检查
        if hasattr(self, "_jump_check_timer"):
            self.root.after_cancel(self._jump_check_timer)

        # 2秒后检查是否应该跳转
        self._jump_check_timer = self.root.after(2000, self._check_and_jump)

    def _check_and_jump(self):
        """检查并执行跳转"""
        import time

        current_time = time.time()

        # 检查是否在等待期间有新的操作
        if current_time - self.last_operation_time >= 1.8:  # 允许一些时间误差
            # 记录最后一帧的停留时间（仅在录制时）
            if (
                self.is_recording_timing
                and self.time_recorder
                and hasattr(self, "current_image_id")
                and self.current_keyframe_index >= 0
            ):

                keyframes = self.get_keyframes(self.current_image_id)
                if keyframes and self.current_keyframe_index < len(keyframes):
                    current_keyframe_id = keyframes[self.current_keyframe_index][0]
                    self.time_recorder.record_keyframe_timing(current_keyframe_id)
                    print(f"记录最后一帧 {self.current_keyframe_index + 1} 的停留时间")

            # 检查是否应该智能跳转到第一帧
            if self._should_auto_jump_to_first():
                print("智能跳转到第一帧开始播放")
                self.current_keyframe_index = 0

                # 获取关键帧并跳转
                keyframes = self.get_keyframes(self.current_image_id)
                if keyframes:
                    _, target_position, _ = keyframes[0]
                    # 直接跳转到第一帧
                    self.canvas.yview_moveto(target_position)
                    if self.second_window and self.sync_enabled:
                        self.sync_projection_screen_absolute()

                    # 更新指示器和预览线
                    self.update_keyframe_indicators()
                    self.update_preview_lines()

                    # 确保投影窗口也更新
                    if self.second_window and self.sync_enabled:
                        self.update_projection()

                # 清除操作历史，准备新的播放周期
                self.keyframe_operation_history = []
            else:
                print("用户可能在最后一帧练习，不自动跳转")

    def smooth_scroll_to(self, target_position):
        """使用基于时间的平滑滚动实现更丝滑的效果"""
        # 获取当前位置
        current_position = self.canvas.yview()[0]

        # 如果位置相同，无需滚动
        if abs(current_position - target_position) < 0.001:
            return

        # 使用统一的滚动速度
        duration = self.scroll_duration

        # 记录初始状态
        start_position = current_position
        distance = target_position - current_position
        start_time = time.time()

        # 取消之前可能正在进行的滚动动画
        if hasattr(self, "_scroll_animation_id"):
            self.root.after_cancel(self._scroll_animation_id)

        # 执行平滑滚动动画
        def animation_frame():
            # 计算当前时间点
            current_time = time.time()
            elapsed = current_time - start_time

            # 检查是否完成动画
            if elapsed >= duration:
                # 动画结束，直接设置到目标位置
                self.canvas.yview_moveto(target_position)
                if self.second_window and self.sync_enabled:
                    self.sync_projection_screen_absolute()

                # 强制更新预览线
                self.update_preview_lines()
                return

            # 计算进度比例(0-1)
            progress = elapsed / duration

            # 使用曲线函数使动画更自然
            # 这是一个三次贝塞尔缓动函数，提供更好的加速和减速感
            t = progress
            ease = 3 * t * t - 2 * t * t * t  # 平滑的S曲线

            # 计算新位置
            new_position = start_position + distance * ease

            # 应用新位置
            self.canvas.yview_moveto(new_position)

            # 投影同步
            if self.second_window and self.sync_enabled:
                self.sync_projection_screen_absolute()

            # 固定动画延迟
            self._scroll_animation_id = self.root.after(16, animation_frame)

        # 开始动画
        animation_frame()

    def ease_in_out_quart(self, t):
        """四次缓动函数，更平滑"""
        if t < 0.5:
            return 8 * t * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 4) / 2

    def create_keyframe_indicator(self):
        """创建关键帧指示器"""
        # 创建指示器框架
        self.indicator_frame = tk.Canvas(
            self.right_frame, width=20, bg="#000000", highlightthickness=0
        )
        self.indicator_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定点击事件
        self.indicator_frame.bind("<Button-1>", self.on_indicator_click)

    def update_keyframe_indicators(self):
        """更新关键帧指示器"""
        if not hasattr(self, "indicator_frame"):
            return

        # 清除现有标记
        self.indicator_frame.delete("all")
        self.keyframe_markers.clear()

        if not hasattr(self, "current_image_id"):
            return

        # 获取当前图片的关键帧
        keyframes = self.get_keyframes(self.current_image_id)
        if not keyframes:
            return

        # 获取指示器高度
        height = self.indicator_frame.winfo_height()

        # 绘制关键帧标记
        for i, (kf_id, position, order_index) in enumerate(keyframes):
            y = position * height

            # 当前帧使用绿色，其他帧使用红色
            if i == self.current_keyframe_index:
                color = "#00FF00"  # 当前帧为绿色
                width = 20  # 当前帧使用更粗的线条
            else:
                color = "#FF0000"  # 其他帧为红色
                width = 17  # 其他帧使用稍细的线条

            # 绘制横线标记
            marker = self.indicator_frame.create_line(
                2, y, 18, y, fill=color, width=width, tags=f"marker_{kf_id}"
            )

            # 在线条两端添加圆点装饰
            self.indicator_frame.create_oval(
                1,
                y - 2,
                5,
                y + 2,  # 左侧圆点
                fill=color,
                outline=color,
                tags=f"marker_{kf_id}",
            )
            self.indicator_frame.create_oval(
                15,
                y - 2,
                19,
                y + 2,  # 右侧圆点
                fill=color,
                outline=color,
                tags=f"marker_{kf_id}",
            )

            # 存储标记信息
            self.keyframe_markers.append(
                {"id": kf_id, "position": position, "marker": marker, "y": y}
            )

    def on_indicator_click(self, event):
        """处理指示器点击事件"""
        if not self.keyframe_markers:
            return

        clicked_y = event.y
        closest_marker = min(
            self.keyframe_markers, key=lambda m: abs(m["y"] - clicked_y)
        )

        # 查找点击的关键帧索引
        keyframes = self.get_keyframes(self.current_image_id)
        for i, (kf_id, _, _) in enumerate(keyframes):
            if kf_id == closest_marker["id"]:
                self.current_keyframe_index = i
                break

        # 平滑滚动到对应位置
        self.smooth_scroll_to(closest_marker["position"])

        # 更新指示器
        self.update_keyframe_indicators()

    def add_current_keyframe(self):
        """添加当前位置为关键帧"""
        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        # 获取当前滚动位置
        position = self.canvas.yview()[0]

        # 添加关键帧
        if self.add_keyframe(self.current_image_id, position):
            # 更新指示器
            self.update_keyframe_indicators()
        else:
            messagebox.showerror("错误", "添加关键帧失败")

    def toggle_timing_recording(self):
        """切换时间录制状态"""
        if not self.time_recorder:
            messagebox.showerror("错误", "时间录制模块未初始化")
            return

        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        # 检查是否为原图模式
        if self.original_mode and hasattr(self, "yuantu_manager"):
            # 原图模式录制
            self._toggle_original_mode_recording()
        else:
            # 普通关键帧录制
            self._toggle_normal_recording()

    def _toggle_normal_recording(self):
        """普通关键帧录制"""
        if not self.is_recording_timing:
            # 开始录制
            if self.time_recorder.start_recording(self.current_image_id):
                self.is_recording_timing = True
                self.btn_record.config(text="停止", bg="#ff6666")
                self.update_button_status()
                print("开始录制关键帧时间差，请按顺序切换关键帧")
            else:
                messagebox.showerror("错误", "开始录制失败")
        else:
            # 停止录制前，记录当前帧的停留时间
            if self.current_keyframe_index >= 0:
                keyframes = self.get_keyframes(self.current_image_id)
                if keyframes and self.current_keyframe_index < len(keyframes):
                    current_keyframe_id = keyframes[self.current_keyframe_index][0]
                    self.time_recorder.record_keyframe_timing(current_keyframe_id)
                    # print(f"记录最终帧 {self.current_keyframe_index + 1} (ID:{current_keyframe_id}) 的停留时间（停止录制）")

            # 停止录制
            if self.time_recorder.stop_recording():
                self.is_recording_timing = False
                self.btn_record.config(text="录制", bg="#ffcccc")
                self.update_button_status()
                # print("录制完成，时间差录制已完成")
            else:
                messagebox.showerror("错误", "停止录制失败")

    def _toggle_original_mode_recording(self):
        """原图模式录制"""
        if not self.time_recorder.is_original_mode_recording:
            # 开始原图模式录制
            similar_images = self.yuantu_manager.similar_images
            if not similar_images:
                messagebox.showinfo(
                    "提示", "当前图片没有相似图片，无法进行原图模式录制"
                )
                return

            # 获取标记类型
            mark_type = "loop"  # 默认循环模式
            if hasattr(self, "current_image_id"):
                # 检查图片本身的标记类型
                img_mark_type = self.yuantu_manager.get_original_mark_type(
                    "image", self.current_image_id
                )
                if img_mark_type:
                    mark_type = img_mark_type
                else:
                    # 检查图片所在文件夹的标记类型
                    result = self.safe_db_execute(
                        "SELECT folder_id FROM images WHERE id = ?",
                        (self.current_image_id,),
                        fetch="one",
                    )
                    if result and result[0]:
                        folder_mark_type = self.yuantu_manager.get_original_mark_type(
                            "folder", result[0]
                        )
                        if folder_mark_type:
                            mark_type = folder_mark_type

            if self.time_recorder.start_original_mode_recording(
                self.current_image_id, similar_images, mark_type
            ):
                self.is_recording_timing = True
                self.btn_record.config(text="停止", bg="#ff6666")
                self.update_button_status()
                print(
                    f"开始原图模式录制，相似图片数量: {len(similar_images)}，标记类型: {mark_type}"
                )
                print("请使用方向键或鼠标在相似图片之间切换来录制切换时间")
            else:
                messagebox.showerror("错误", "开始原图模式录制失败")
        else:
            # 停止原图模式录制
            if self.time_recorder.stop_original_mode_recording():
                self.is_recording_timing = False
                self.btn_record.config(text="录制", bg="#ffcccc")
                print("原图模式录制完成")

                # 延迟更新按钮状态，确保数据库操作完成
                self.root.after(100, self.update_button_status)
            else:
                messagebox.showerror("错误", "停止原图模式录制失败")

    def _on_auto_play_finished(self):
        """自动播放结束时的回调函数"""
        # 更新播放状态
        self.is_auto_playing = False

        # 更新按钮状态
        self.btn_play.config(text="播放", bg="#f0f0f0")  # 重置背景颜色
        self.update_button_status()

        # print("自动播放结束，已切换回播放按钮状态")

    def toggle_auto_play(self):
        """切换自动播放状态"""
        if not self.auto_player:
            messagebox.showerror("错误", "自动播放模块未初始化")
            return

        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        # 检查是否为原图模式
        if self.original_mode and hasattr(self, "yuantu_manager"):
            # 原图模式播放
            self._toggle_original_mode_auto_play()
        else:
            # 普通关键帧播放
            self._toggle_normal_auto_play()

    def _toggle_normal_auto_play(self):
        """普通关键帧自动播放"""
        if not self.is_auto_playing:
            # 开始自动播放
            if self.auto_player.start_auto_play(self.current_image_id):
                self.is_auto_playing = True
                self.btn_play.config(text="停止", bg="#90EE90")
                self.update_button_status()
            else:
                messagebox.showerror("错误", "该图片没有录制的时间序列数据")
        else:
            # 停止自动播放
            if self.auto_player.stop_auto_play():
                self.is_auto_playing = False
                self.btn_play.config(text="播放", bg="#f0f0f0")  # 重置背景颜色
                self.update_button_status()

    def _toggle_original_mode_auto_play(self):
        """原图模式自动播放"""
        if not self.auto_player.is_original_mode_playing:
            # 开始原图模式自动播放
            similar_images = self.yuantu_manager.similar_images
            if not similar_images:
                messagebox.showinfo(
                    "提示", "当前图片没有相似图片，无法进行原图模式播放"
                )
                return

            if self.auto_player.start_original_mode_auto_play(
                self.current_image_id, similar_images
            ):
                self.is_auto_playing = True
                self.btn_play.config(text="停止", bg="#90EE90")
                self.update_button_status()
                print(f"开始原图模式自动播放，相似图片数量: {len(similar_images)}")
            else:
                messagebox.showerror("错误", "该图片没有录制的原图模式时间序列数据")
        else:
            # 停止原图模式自动播放
            if self.auto_player.stop_original_mode_auto_play():
                self.is_auto_playing = False
                self.btn_play.config(text="播放", bg="#f0f0f0")  # 重置背景颜色
                self.update_button_status()

    def set_play_count(self):
        """设置播放次数（自定义输入）"""
        # 创建输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("设置播放次数")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (dialog.winfo_screenheight() // 2) - (150 // 2)
        dialog.geometry(f"300x150+{x}+{y}")

        # 说明文字
        tk.Label(dialog, text="请输入播放次数：", font=("Arial", 10)).pack(pady=10)

        # 输入框
        entry_frame = tk.Frame(dialog)
        entry_frame.pack(pady=5)

        tk.Label(entry_frame, text="次数：").pack(side=tk.LEFT)
        entry = tk.Entry(entry_frame, width=10)
        entry.pack(side=tk.LEFT, padx=5)
        entry.insert(
            0, str(self.target_play_count) if self.target_play_count != -1 else "∞"
        )
        entry.select_range(0, tk.END)
        entry.focus()

        # 提示文字
        tk.Label(
            dialog, text="输入数字或 ∞ 表示无限循环", font=("Arial", 8), fg="gray"
        ).pack(pady=5)

        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        def validate_and_save():
            """验证输入并保存设置"""
            value = entry.get().strip()

            if value == "∞" or value.lower() == "in":
                new_count = -1
            else:
                try:
                    new_count = int(value)
                    if new_count <= 0:
                        messagebox.showerror("错误", "播放次数必须大于0")
                        return
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的数字或 ∞")
                    return

            # 保存设置
            self.target_play_count = new_count
            self.save_play_count_setting()

            # 更新按钮显示
            self._update_play_count_button()

            # 同步到auto_player
            if self.auto_player:
                loop_enabled = self.target_play_count == -1
                self.auto_player.set_loop_mode(loop_enabled)
                if hasattr(self.auto_player, "target_play_count"):
                    self.auto_player.target_play_count = self.target_play_count

            print(
                f"播放次数设定为: {'无限循环' if self.target_play_count == -1 else f'{self.target_play_count}次'}"
            )
            dialog.destroy()

        def cancel():
            """取消设置"""
            dialog.destroy()

        # 按钮
        tk.Button(button_frame, text="确定", command=validate_and_save, width=8).pack(
            side=tk.LEFT, padx=5
        )
        tk.Button(button_frame, text="取消", command=cancel, width=8).pack(
            side=tk.LEFT, padx=5
        )

        # 绑定回车键
        entry.bind("<Return>", lambda e: validate_and_save())
        entry.bind("<Escape>", lambda e: cancel())

    def _update_play_count_button(self):
        """更新播放次数按钮显示"""
        if self.target_play_count == -1:
            text = "∞次"
        else:
            text = f"{self.target_play_count}次"

        if hasattr(self, "btn_loop"):
            self.btn_loop.config(text=text)

    def save_play_count_setting(self):
        """保存播放次数设置到数据库"""
        try:
            self.safe_db_execute(
                """
                INSERT OR REPLACE INTO settings (key, value)
                VALUES ('play_count', ?)
            """,
                (str(self.target_play_count),),
            )
            print(f"播放次数设置已保存: {self.target_play_count}")
        except Exception as e:
            print(f"保存播放次数设置失败: {e}")

    def load_play_count_setting(self):
        """从数据库加载播放次数设置"""
        try:
            result = self.safe_db_execute(
                "SELECT value FROM settings WHERE key = ?", ("play_count",), fetch=True
            )

            if result and len(result) > 0:
                # result是列表，每个元素是元组，取第一个元组的第一个元素
                value = result[0][0]

                if value == "-1":
                    self.target_play_count = -1
                else:
                    try:
                        count = int(value)
                        if count > 0:
                            self.target_play_count = count
                    except ValueError:
                        pass  # 使用默认值

                # print(f"加载播放次数设置: {self.target_play_count}")
            else:
                print("使用默认播放次数设置: 5次")

        except Exception as e:
            print(f"加载播放次数设置失败: {e}")
            # 使用默认值
            self.target_play_count = 5

    def clear_timing_data(self):
        """清除当前图片的时间录制数据"""
        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        if not self.time_recorder:
            messagebox.showerror("错误", "时间录制模块未初始化")
            return

        # 检查是否为原图模式
        if self.original_mode and hasattr(self, "yuantu_manager"):
            # 原图模式清除
            self._clear_original_mode_timing_data()
        else:
            # 普通关键帧清除
            self._clear_normal_timing_data()

    def _clear_normal_timing_data(self):
        """清除普通关键帧时间数据"""
        # 检查是否有时间数据
        if not self.time_recorder.has_timing_data(self.current_image_id):
            messagebox.showinfo("提示", "当前图片没有录制的时间数据")
            return

        # 确认清除
        result = messagebox.askyesno(
            "确认清除",
            "确定要清除当前图片的所有关键帧时间录制数据吗？\n此操作不可撤销。",
        )

        if result:
            # 先停止正在进行的录制或播放
            if self.is_recording_timing:
                # 清除前先记录当前帧的停留时间
                if self.current_keyframe_index >= 0:
                    keyframes = self.get_keyframes(self.current_image_id)
                    if keyframes and self.current_keyframe_index < len(keyframes):
                        current_keyframe_id = keyframes[self.current_keyframe_index][0]
                        self.time_recorder.record_keyframe_timing(current_keyframe_id)
                        print(
                            f"记录最终帧 {self.current_keyframe_index + 1} (ID:{current_keyframe_id}) 的停留时间（清除前）"
                        )

                self.time_recorder.stop_recording()
                self.is_recording_timing = False
                self.btn_record.config(text="录制", bg="#e8f5e8")

            if self.is_auto_playing:
                self.auto_player.stop_auto_play()
                self.is_auto_playing = False
                self.btn_play.config(text="播放", bg="#e8f0")

            # 清除时间数据
            if self.time_recorder.clear_timing_data(self.current_image_id):
                self.update_button_status()
                print(
                    f"清除完成：已清除图片 {self.current_image_id} 的关键帧时间录制数据"
                )
            else:
                messagebox.showerror("错误", "清除时间数据失败")

    def _clear_original_mode_timing_data(self):
        """清除原图模式时间数据"""
        # 检查是否有原图模式时间数据
        if not self.time_recorder.has_original_mode_timing_data(self.current_image_id):
            messagebox.showinfo("提示", "当前图片没有录制的原图模式时间数据")
            return

        # 确认清除
        result = messagebox.askyesno(
            "确认清除",
            "确定要清除当前图片的所有原图模式时间录制数据吗？\n此操作不可撤销。",
        )

        if result:
            # 先停止正在进行的录制或播放
            if self.is_recording_timing:
                self.time_recorder.stop_original_mode_recording()
                self.is_recording_timing = False
                self.btn_record.config(text="录制", bg="#e8f5e8")

            if self.is_auto_playing:
                self.auto_player.stop_original_mode_auto_play()
                self.is_auto_playing = False
                self.btn_play.config(text="播放", bg="#e8f0")

            # 清除原图模式时间数据
            if self.time_recorder.clear_original_mode_timing_data(
                self.current_image_id
            ):
                self.update_button_status()
                print(
                    f"清除完成：已清除图片 {self.current_image_id} 的原图模式时间录制数据"
                )
            else:
                messagebox.showerror("错误", "清除原图模式时间数据失败")

    def show_script_info(self):
        """显示和编辑脚本信息"""
        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        if not self.time_recorder:
            messagebox.showerror("错误", "时间录制模块未初始化")
            return

        # 检查是否为原图模式
        if self.original_mode and hasattr(self, "yuantu_manager"):
            # 原图模式脚本
            self._show_original_mode_script_info()
        else:
            # 普通关键帧脚本
            self._show_normal_script_info()

    def _show_normal_script_info(self):
        """显示普通关键帧脚本信息"""
        # 获取当前图片的时间数据
        timing_sequence = self.time_recorder.get_timing_sequence(self.current_image_id)
        if not timing_sequence:
            messagebox.showinfo("提示", "当前图片没有录制的时间数据")
            return

        # 创建脚本信息窗口
        self._create_script_info_window(timing_sequence)

    def _show_original_mode_script_info(self):
        """显示原图模式脚本信息"""
        # 获取原图模式时间数据
        timing_sequence = self.time_recorder.get_original_mode_timing_sequence(
            self.current_image_id
        )
        if not timing_sequence:
            messagebox.showinfo("提示", "当前图片没有录制的原图模式时间数据")
            return

        # 获取相似图片列表
        similar_images = self.yuantu_manager.similar_images
        if not similar_images:
            messagebox.showinfo("提示", "当前图片没有相似图片")
            return

        # 获取标记类型
        mark_type = "loop"  # 默认循环模式
        if hasattr(self, "current_image_id"):
            # 检查图片本身的标记类型
            img_mark_type = self.yuantu_manager.get_original_mark_type(
                "image", self.current_image_id
            )
            if img_mark_type:
                mark_type = img_mark_type
            else:
                # 检查图片所在文件夹的标记类型
                result = self.safe_db_execute(
                    "SELECT folder_id FROM images WHERE id = ?",
                    (self.current_image_id,),
                    fetch="one",
                )
                if result and result[0]:
                    folder_mark_type = self.yuantu_manager.get_original_mark_type(
                        "folder", result[0]
                    )
                    if folder_mark_type:
                        mark_type = folder_mark_type

        # 生成原图模式脚本内容
        from keytime import (
            format_original_mode_script_for_edit,
            generate_original_mode_script,
        )

        script_content = generate_original_mode_script(
            self.current_image_id, timing_sequence, similar_images, mark_type
        )
        edit_content = format_original_mode_script_for_edit(
            timing_sequence, similar_images
        )

        # 创建原图模式脚本信息窗口
        self._create_original_mode_script_info_window(
            timing_sequence, similar_images, script_content, edit_content, mark_type
        )

    def _create_script_info_window(self, timing_sequence):
        """创建脚本信息编辑窗口"""
        # 创建新窗口
        script_window = tk.Toplevel(self.root)
        script_window.title("脚本信息编辑")
        script_window.geometry("600x600")
        script_window.transient(self.root)
        script_window.grab_set()

        # 创建主框架
        main_frame = tk.Frame(script_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        title_label = tk.Label(
            main_frame, text="关键帧时间脚本", font=(self.default_font, 14, "bold")
        )
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = tk.Label(
            main_frame,
            text="格式: 帧序号 时间(秒) | 可以修改时间值并保存",
            font=(self.default_font, 10),
        )
        info_label.pack(pady=(0, 10))

        # 保存按钮（移到上方）
        save_btn = tk.Button(
            main_frame,
            text="保存修改",
            command=lambda: self._save_script_changes(
                script_text, script_window, timing_sequence
            ),
            bg="#ccffcc",
            font=(self.default_font, 10),
        )
        save_btn.pack(pady=(0, 10))

        # 创建滚动文本框
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 文本框和滚动条
        text_scroll = tk.Scrollbar(text_frame)
        text_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        script_text = tk.Text(
            text_frame,
            yscrollcommand=text_scroll.set,
            font=(self.default_font, 11),
            wrap=tk.WORD,
        )
        script_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scroll.config(command=script_text.yview)

        # 填充脚本内容
        script_content = self._format_script_content(timing_sequence)
        script_text.insert(tk.END, script_content)

        # 应用当前字体设置
        self._apply_font_to_script_window(
            script_window, title_label, info_label, save_btn, script_text
        )

        # 使窗口居中
        script_window.update_idletasks()
        x = (script_window.winfo_screenwidth() // 2) - (
            script_window.winfo_width() // 2
        )
        y = (script_window.winfo_screenheight() // 2) - (
            script_window.winfo_height() // 2
        )
        script_window.geometry(f"+{x}+{y}")

    def _format_script_content(self, timing_sequence):
        """格式化脚本内容"""
        # 获取关键帧映射（ID到帧号）
        keyframe_mapping = self._get_keyframe_mapping()

        content = ""
        for i, (keyframe_id, duration, sequence_order) in enumerate(timing_sequence):
            # 获取关键帧的实际帧号（1,2,3,4,5）
            frame_number = keyframe_mapping.get(keyframe_id, "?")
            content += f"{frame_number}  {duration:6.1f}s\n"

        return content

    def _get_keyframe_mapping(self):
        """获取关键帧ID到帧号的映射"""
        if not hasattr(self, "current_image_id"):
            return {}

        keyframes = self.get_keyframes(self.current_image_id)
        mapping = {}
        for i, (keyframe_id, position, order) in enumerate(keyframes):
            mapping[keyframe_id] = i + 1  # 帧号从1开始

        return mapping

    def _apply_font_to_script_window(
        self, script_window, title_label, info_label, save_btn, script_text
    ):
        """应用字体设置到脚本窗口"""
        try:
            # 更新标题字体
            title_label.configure(
                font=(self.default_font, self.large_font_size, "bold")
            )

            # 更新说明文字字体
            info_label.configure(font=(self.default_font, self.menu_font_size))

            # 更新保存按钮字体
            save_btn.configure(font=(self.default_font, self.menu_font_size))

            # 更新文本框字体
            script_text.configure(font=(self.default_font, self.font_size))

        except Exception as e:
            print(f"应用脚本窗口字体失败: {e}")

    def _save_script_changes(self, script_text, script_window, original_timing):
        """保存脚本修改"""
        try:
            # 获取文本内容
            content = script_text.get("1.0", tk.END)
            lines = content.strip().split("\n")

            # 解析修改后的时间数据
            new_timings = []
            line_num = 0

            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                # 解析格式: "1  10.0s" 或 "1 10.0"
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        _ = int(parts[0])  # frame_num - 验证格式但不使用
                        time_str = parts[1].rstrip("s")
                        duration = float(time_str)

                        if duration < 0:
                            raise ValueError("时间不能为负数")

                        # 使用原始的keyframe_id和sequence_order
                        if line_num < len(original_timing):
                            original_keyframe_id = original_timing[line_num][0]
                            original_sequence_order = original_timing[line_num][2]
                            new_timings.append(
                                (
                                    original_keyframe_id,
                                    duration,
                                    original_sequence_order,
                                )
                            )

                        line_num += 1

                    except (ValueError, IndexError):
                        messagebox.showerror(
                            "格式错误",
                            f"第{line_num + 1}行格式错误: {line}\n请使用格式: 帧序号 时间",
                        )
                        return

            if not new_timings:
                messagebox.showerror("错误", "没有找到有效的时间数据")
                return

            if len(new_timings) != len(original_timing):
                result = messagebox.askyesno(
                    "确认修改",
                    f"原始数据有{len(original_timing)}帧，新数据有{len(new_timings)}帧\n是否继续保存？",
                )
                if not result:
                    return

            # 保存到数据库
            if self._update_timing_data(new_timings):
                messagebox.showinfo("保存成功", "脚本信息已更新")
                script_window.destroy()
                # 更新按钮状态
                self.update_button_status()
            else:
                messagebox.showerror("保存失败", "无法保存脚本信息到数据库")

        except Exception as e:
            messagebox.showerror("错误", f"保存脚本时发生错误: {e}")

    def _update_timing_data(self, new_timings):
        """更新数据库中的时间数据（优化版本：使用事务批处理）"""
        try:
            # 准备批量操作
            operations = [
                {
                    "query": "DELETE FROM keyframe_timings WHERE image_id = ?",
                    "params": (self.current_image_id,),
                }
            ]

            # 添加插入操作
            for keyframe_id, duration, sequence_order in new_timings:
                operations.append(
                    {
                        "query": """INSERT INTO keyframe_timings
                               (image_id, keyframe_id, duration, sequence_order)
                               VALUES (?, ?, ?, ?)""",
                        "params": (
                            self.current_image_id,
                            keyframe_id,
                            duration,
                            sequence_order,
                        ),
                    }
                )

            # 使用数据库管理器的批量操作
            success = self.db_manager.execute_batch(operations)

            if success:
                print(
                    f"已更新图片 {self.current_image_id} 的时间数据，共 {len(new_timings)} 条记录"
                )

            return success

        except Exception as e:
            print(f"更新时间数据失败: {e}")
            return False

    def _create_original_mode_script_info_window(
        self, timing_sequence, similar_images, script_content, edit_content, mark_type
    ):
        """创建原图模式脚本信息编辑窗口"""
        # 创建新窗口
        script_window = tk.Toplevel(self.root)
        script_window.title("原图模式脚本信息编辑")
        script_window.geometry("800x700")
        script_window.transient(self.root)
        script_window.grab_set()

        # 创建主框架
        main_frame = tk.Frame(script_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        title_label = tk.Label(
            main_frame, text="原图模式时间脚本", font=(self.default_font, 14, "bold")
        )
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = tk.Label(
            main_frame,
            text=f"标记类型: {mark_type} | 相似图片数量: {len(similar_images)} | 可以修改时间值并保存",
            font=(self.default_font, 10),
        )
        info_label.pack(pady=(0, 10))

        # 保存按钮
        save_btn = tk.Button(
            main_frame,
            text="保存修改",
            command=lambda: self._save_original_mode_script_changes(
                script_text, script_window, timing_sequence, similar_images
            ),
            bg="#ccffcc",
            font=(self.default_font, 10),
        )
        save_btn.pack(pady=(0, 10))

        # 创建滚动文本框
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 文本框和滚动条
        text_scroll = tk.Scrollbar(text_frame)
        text_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        script_text = tk.Text(
            text_frame,
            yscrollcommand=text_scroll.set,
            font=(self.default_font, 11),
            wrap=tk.WORD,
        )
        script_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scroll.config(command=script_text.yview)

        # 填充脚本内容
        script_text.insert(tk.END, edit_content)

        # 应用当前字体设置
        self._apply_font_to_script_window(
            script_window, title_label, info_label, save_btn, script_text
        )

        # 使窗口居中
        script_window.update_idletasks()
        x = (script_window.winfo_screenwidth() // 2) - (
            script_window.winfo_width() // 2
        )
        y = (script_window.winfo_screenheight() // 2) - (
            script_window.winfo_height() // 2
        )
        script_window.geometry(f"+{x}+{y}")

    def _save_original_mode_script_changes(
        self, script_text, script_window, original_timing, similar_images
    ):
        """保存原图模式脚本修改"""
        try:
            # 获取修改后的内容
            new_content = script_text.get("1.0", tk.END).strip()

            # 解析新的时间序列
            from keytime import (
                parse_original_mode_script,
                validate_original_mode_timing_sequence,
            )

            new_timings = parse_original_mode_script(new_content)

            # 验证时间序列
            if not validate_original_mode_timing_sequence(new_timings, similar_images):
                messagebox.showerror("验证失败", "时间序列验证失败，请检查格式是否正确")
                return

            # 确认保存
            result = messagebox.askyesno(
                "确认保存",
                f"确定要保存修改后的时间序列吗？\n原序列长度: {len(original_timing)}\n新序列长度: {len(new_timings)}",
            )

            if result:
                # 更新数据库中的时间数据
                if self._update_original_mode_timing_data(new_timings):
                    messagebox.showinfo("保存成功", "原图模式时间序列已更新")
                    script_window.destroy()
                    # 更新按钮状态
                    self.update_button_status()
                else:
                    messagebox.showerror("保存失败", "无法保存原图模式脚本信息到数据库")

        except Exception as e:
            messagebox.showerror("错误", f"保存原图模式脚本时发生错误: {e}")

    def _update_original_mode_timing_data(self, new_timings):
        """更新数据库中的原图模式时间数据（优化版本：使用事务批处理）"""
        try:
            # 准备批量操作
            operations = [
                {
                    "query": "DELETE FROM original_mode_timings WHERE base_image_id = ?",
                    "params": (self.current_image_id,),
                }
            ]

            # 添加插入操作
            for i, (from_image_id, to_image_id, duration) in enumerate(new_timings):
                operations.append(
                    {
                        "query": """INSERT INTO original_mode_timings
                               (base_image_id, from_image_id, to_image_id, duration, sequence_order, mark_type)
                               VALUES (?, ?, ?, ?, ?, ?)""",
                        "params": (
                            self.current_image_id,
                            from_image_id,
                            to_image_id,
                            duration,
                            i,
                            "loop",
                        ),
                    }
                )

            # 使用数据库管理器的批量操作
            success = self.db_manager.execute_batch(operations)

            if success:
                print(
                    f"已更新图片 {self.current_image_id} 的原图模式时间数据，共 {len(new_timings)} 条记录"
                )

            return success

        except Exception as e:
            print(f"更新原图模式时间数据失败: {e}")
            return False

    def update_button_status(self):
        """更新按钮状态显示"""
        # 更新播放次数按钮显示
        if hasattr(self, "target_play_count"):
            self._update_play_count_button()

        # 检查是否有选中的图片和时间数据
        has_current_image = hasattr(self, "current_image_id")

        # 根据模式检查时间数据
        has_timing_data = False
        if has_current_image and self.time_recorder:
            if self.original_mode and hasattr(self, "yuantu_manager"):
                # 原图模式：检查原图模式时间数据
                has_timing_data = self.time_recorder.has_original_mode_timing_data(
                    self.current_image_id
                )
            else:
                # 普通模式：检查关键帧时间数据
                has_timing_data = self.time_recorder.has_timing_data(
                    self.current_image_id
                )

        # 禁用/启用相关按钮
        if self.is_recording_timing:
            # 录制时禁用播放、清除和脚本
            self.btn_play.config(state="disabled")
            self.btn_clear_timing.config(state="disabled")
            self.btn_script_info.config(state="disabled")
        elif self.is_auto_playing:
            # 播放时禁用录制、清除和脚本
            self.btn_record.config(state="disabled")
            self.btn_clear_timing.config(state="disabled")
            self.btn_script_info.config(state="disabled")
        else:
            # 都不在进行时的状态控制
            self.btn_record.config(state="normal")

            # 播放按钮：只有有时间数据时才能播放，并确保颜色正确
            if has_timing_data:
                self.btn_play.config(state="normal", bg="#f0f0f0")  # 确保停止时颜色正确
            else:
                self.btn_play.config(state="disabled", bg="#f0f0f0")

            # 清除时间按钮：只有有时间数据时才能清除
            if has_timing_data:
                self.btn_clear_timing.config(state="normal")
            else:
                self.btn_clear_timing.config(state="disabled")

            # 脚本信息按钮：只有有时间数据时才能查看
            if has_timing_data:
                self.btn_script_info.config(state="normal")
            else:
                self.btn_script_info.config(state="disabled")

    def show_contact_info(self):
        """显示联系方式信息"""
        contact_info = "联系人: 静等\n电话: 18162074638"
        messagebox.showinfo("作者", contact_info)

    def handle_arrow_key(self, direction):
        """处理方向键事件，在原图模式下切换图片"""
        # 只在原图模式下响应方向键
        if self.original_mode and hasattr(self, "current_image_id"):
            self.yuantu_manager.find_and_switch_similar_image(direction=direction)

    def handle_pageup_key(self):
        """处理PageUp键事件"""
        if self.original_mode and hasattr(self, "current_image_id"):
            # 原图模式播放时间修正逻辑
            if (
                self.is_auto_playing
                and self.auto_player
                and self.auto_player.is_original_mode_playing
            ):
                # 获取当前和目标图片信息进行时间修正
                current_image_id = self.current_image_id
                similar_images = self.yuantu_manager.similar_images
                if similar_images:
                    # 找到上一张图片
                    current_index = next(
                        (
                            i
                            for i, (img_id, _, _) in enumerate(similar_images)
                            if img_id == current_image_id
                        ),
                        -1,
                    )
                    if current_index > 0:
                        prev_image_id = similar_images[current_index - 1][0]
                        self.auto_player.record_original_mode_manual_operation(
                            prev_image_id, current_image_id
                        )

            # 原图模式下：切换到上一张相似图片
            self.yuantu_manager.find_and_switch_similar_image(direction="prev")
        else:
            # 正常模式下：跳转到上一个关键帧
            self.step_to_prev_keyframe()

    def handle_pagedown_key(self):
        """处理PageDown键事件"""
        if self.original_mode and hasattr(self, "current_image_id"):
            # 原图模式播放时间修正逻辑
            if (
                self.is_auto_playing
                and self.auto_player
                and self.auto_player.is_original_mode_playing
            ):
                # 获取当前和目标图片信息进行时间修正
                current_image_id = self.current_image_id
                similar_images = self.yuantu_manager.similar_images
                if similar_images:
                    # 找到下一张图片
                    current_index = next(
                        (
                            i
                            for i, (img_id, _, _) in enumerate(similar_images)
                            if img_id == current_image_id
                        ),
                        -1,
                    )
                    if current_index >= 0 and current_index < len(similar_images) - 1:
                        next_image_id = similar_images[current_index + 1][0]
                        self.auto_player.record_original_mode_manual_operation(
                            current_image_id, next_image_id
                        )

            # 原图模式下：切换到下一张相似图片
            self.yuantu_manager.find_and_switch_similar_image(direction="next")
        else:
            # 正常模式下：跳转到下一个关键帧
            self.step_to_next_keyframe()

    def handle_escape_key(self):
        """处理ESC按键 - 结束投影"""
        if self.second_window:
            # 关闭投影窗口
            self.second_window.destroy()
            self.second_window = None
            self.second_canvas = None
            self.btn_show.configure(bg="#f0f0f0", text="投影")  # 恢复原色和原文本
            self.sync_enabled = False

            # ESC键关闭投影时也要清理全局热键
            self.cleanup_global_hotkeys()
            self.global_hotkeys_enabled = False
            print("ESC键：投影已结束，全局热键已禁用")

    # 删除播放、录制、次数相关的按键处理函数

    def get_effective_screen_size(self, is_projection_screen=False):
        """
        统一计算有效显示区域 - 修复主屏幕和投影屏幕显示不一致问题

        Args:
            is_projection_screen: 是否为投影屏幕

        Returns:
            tuple: (effective_width, effective_height)
        """
        if is_projection_screen:
            # 投影屏幕使用完整屏幕尺寸
            selected = self.screen_combo.current()
            screen = self.screens[selected]
            return screen.width, screen.height
        else:
            # 主屏幕使用画布尺寸，但需要确保与投影屏幕的计算方式一致
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # 为了与投影屏幕保持一致，我们需要在原图模式下使用相同的比例计算
            return canvas_width, canvas_height

    def sync_projection_screen_absolute(self):
        """使用绝对像素位置同步投影屏幕，解决滚动不一致问题 - 黄字模式优化版本"""
        if not self.second_canvas or not self.sync_enabled or not self.image:
            return

        try:

            # 获取主屏幕当前的绝对像素位置
            main_scroll_top = self.canvas.canvasy(0)  # 主屏幕顶部对应的图片像素位置

            # 获取主屏幕的图片尺寸信息
            main_width, main_height = self.get_effective_screen_size(
                is_projection_screen=False
            )

            # 计算主屏幕图片的实际高度
            if self.original_mode:
                main_width_ratio = main_width / self.image.width
                main_height_ratio = main_height / self.image.height
                if main_width_ratio < 1 or main_height_ratio < 1:
                    main_scale = min(main_width_ratio, main_height_ratio)
                    main_img_height = int(self.image.height * main_scale)
                else:
                    main_img_height = self.image.height
            else:
                main_base_ratio = main_width / self.image.width
                main_final_ratio = main_base_ratio * self.zoom_ratio
                main_img_height = int(self.image.height * main_final_ratio)

            # 获取投影屏幕的图片尺寸信息
            proj_width, proj_height = self.get_effective_screen_size(
                is_projection_screen=True
            )

            # 计算投影屏幕图片的实际高度
            if self.original_mode:
                proj_width_ratio = proj_width / self.image.width
                proj_height_ratio = proj_height / self.image.height
                if proj_width_ratio < 1 or proj_height_ratio < 1:
                    proj_scale = min(proj_width_ratio, proj_height_ratio)
                    proj_img_height = int(self.image.height * proj_scale)
                else:
                    proj_img_height = self.image.height
            else:
                proj_base_ratio = proj_width / self.image.width
                proj_final_ratio = proj_base_ratio * self.zoom_ratio
                proj_img_height = int(self.image.height * proj_final_ratio)

            # 计算在原始图片上的相对位置（0-1之间）
            original_relative_pos = (
                main_scroll_top / main_img_height if main_img_height > 0 else 0
            )

            # 计算投影屏幕应该滚动到的绝对像素位置
            proj_scroll_top = original_relative_pos * proj_img_height

            # 获取投影屏幕的滚动区域信息
            proj_scroll_region = self.second_canvas.cget("scrollregion").split()
            if len(proj_scroll_region) >= 4:
                proj_scroll_height = float(proj_scroll_region[3])

                # 计算投影屏幕的相对位置
                proj_relative_pos = (
                    proj_scroll_top / proj_scroll_height
                    if proj_scroll_height > 0
                    else 0
                )

                # 限制在有效范围内
                proj_relative_pos = max(0, min(1, proj_relative_pos))

                # 应用到投影屏幕
                self.second_canvas.yview_moveto(proj_relative_pos)

        except Exception as e:
            print(f"绝对位置同步失败: {e}")
            # 降级到原来的同步方式
            self.second_canvas.yview_moveto(self.canvas.yview()[0])

    def clear_current_keyframes(self):
        """清除当前图片的所有关键帧"""
        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        if messagebox.askyesno("确认", "确定要清除所有关键帧吗？"):
            if self.clear_keyframes(self.current_image_id):
                self.current_keyframe_index = -1
                self.update_keyframe_indicators()
                messagebox.showinfo("成功", "清除关键帧成功")
            else:
                messagebox.showerror("错误", "清除关键帧失败")

    def on_hover(self, button, entering):
        """处理按钮悬停效果"""
        # 如果是投影显示按钮且投影窗口存在，保持绿色
        if button == self.btn_show and self.second_window:
            button.configure(bg="#90EE90")
            return

        # 如果是反色按钮且反色已启用，保持绿色
        if button == self.btn_invert and self.is_inverted:
            button.configure(bg="#90EE90")
            return

        # 如果是普通反色按钮且普通反色已启用，保持绿色
        if button == self.btn_simple_invert and self.is_simple_inverted:
            button.configure(bg="#90EE90")
            return

        # 如果是原图模式按钮且原图模式已启用，保持绿色
        if button == self.btn_original and self.original_mode:
            button.configure(bg="#90EE90")
            return

        # 其他情况正常悬停效果
        # 排除联系方式按钮，因为它没有特殊状态
        if button != self.btn_contact:
            button.configure(bg="#e5e5e5" if entering else "#f0f0f0")
        else:
            # 联系方式按钮的悬停效果
            button.configure(bg="#e5e5e5" if entering else "#f0f0f0")

    def show_import_menu(self):
        """显示导入菜单"""
        x = self.btn_import.winfo_rootx()
        y = self.btn_import.winfo_rooty() + self.btn_import.winfo_height()
        self.import_menu.post(x, y)

    def import_single_image(self):
        """导入单个图片"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif *.ti")]
        )
        if file_path:
            try:
                # 获取文件名作为项目名称
                name = Path(file_path).stem
                with sqlite3.connect(self.db_path) as conn:
                    # 检查图片是否已存在
                    cursor = conn.execute(
                        "SELECT id FROM images WHERE path = ?", (file_path,)
                    )
                    existing = cursor.fetchone()
                    if existing:
                        # 如果图片已存在，直接加载该图片
                        image_id = existing[0]
                        messagebox.showinfo("提示", "该图片已存在，将直接打开")
                        self.load_image(file_path)

                        # 在项目树中选中该图片
                        self.project_tree.selection_set(str(image_id))
                        self.project_tree.see(str(image_id))
                        return

                    # 获取当前最大的order_index
                    cursor = conn.execute(
                        "SELECT COALESCE(MAX(order_index), -1) FROM images WHERE folder_id IS NULL"
                    )
                    max_order = cursor.fetchone()[0]

                    # 添加图片（不关联文件夹）
                    cursor = conn.execute(
                        """
                        INSERT INTO images (name, path, folder_id, order_index)
                        VALUES (?, ?, NULL, ?)
                    """,
                        (name, file_path, max_order + 1),
                    )

                    # 获取新插入图片的ID
                    image_id = cursor.lastrowid

                    conn.commit()

                    # 更新项目列表
                    self.load_projects()

                    # 加载新导入的图片
                    self.load_image(file_path)

                    # 在项目树中选中新导入的图片
                    self.project_tree.selection_set(str(image_id))
                    self.project_tree.see(str(image_id))

                    # 移除成功导入的提示消息

            except Exception as e:
                print(f"导入失败: {e}")
                messagebox.showerror("错误", f"导入失败: {e}")

    def import_folder(self):
        """导入整个文件夹"""
        folder_path = filedialog.askdirectory()
        if folder_path:
            try:
                # 获取文件夹名称
                folder_path_obj = Path(folder_path)
                folder_name = folder_path_obj.name

                # 获取所有支持的图片文件
                image_files = []
                extensions = (".jpg", ".jpeg", ".png", ".bmp", ".gi", ".ti")

                # 修改：使用Path.rglob递归扫描所有子文件夹
                for file_path in folder_path_obj.rglob("*"):
                    if file_path.is_file() and file_path.suffix.lower() in extensions:
                        image_files.append(str(file_path))

                if not image_files:
                    messagebox.showinfo("提示", "所选文件夹中没有支持的图片文件")
                    return

                with sqlite3.connect(self.db_path) as conn:
                    # 检查文件夹是否已存在
                    cursor = conn.execute(
                        "SELECT id FROM folders WHERE path = ?", (folder_path,)
                    )
                    folder_result = cursor.fetchone()

                    if not folder_result:
                        # 创建新文件夹记录
                        cursor = conn.execute(
                            """
                            SELECT COALESCE(MAX(order_index), -1)
                            FROM folders
                        """
                        )
                        max_folder_order = cursor.fetchone()[0]

                        cursor = conn.execute(
                            """
                            INSERT INTO folders (name, path, order_index)
                            VALUES (?, ?, ?)
                        """,
                            (folder_name, folder_path, max_folder_order + 1),
                        )
                        folder_id = cursor.lastrowid
                    else:
                        folder_id = folder_result[0]

                    # 获取已存在的图片路径
                    cursor = conn.execute(
                        "SELECT path FROM images WHERE folder_id = ?", (folder_id,)
                    )
                    existing_images = {row[0] for row in cursor.fetchall()}

                    # 过滤出新图片
                    new_images = [f for f in image_files if f not in existing_images]

                    if not new_images and folder_result:
                        messagebox.showinfo("提示", "所有图片都已存在")
                        return

                    # 修改：获取文件夹内所有图片（包括已存在和新增的）进行排序
                    all_images = list(existing_images) + new_images
                    sorted_images = sorted(all_images, key=self.get_sort_key)

                    # 获取所有图片的排序位置映射
                    position_map = {img: idx for idx, img in enumerate(sorted_images)}

                    # 显示进度条
                    progress = tk.Toplevel(self.root)
                    progress.title("导入进度")
                    progress.geometry("300x150")
                    progress.transient(self.root)

                    label = ttk.Label(
                        progress,
                        text="正在导入图片...",
                        font=("Microsoft YaHei UI", 10),
                    )
                    label.pack(pady=10)

                    progress_bar = ttk.Progressbar(
                        progress, length=200, mode="determinate"
                    )
                    progress_bar.pack(pady=10)

                    count_label = ttk.Label(
                        progress,
                        text="0/" + str(len(new_images)),
                        font=("Microsoft YaHei UI", 10),
                    )
                    count_label.pack(pady=10)

                    progress.update()

                    # 检查是否为手动排序文件夹
                    cursor = conn.execute(
                        """
                        SELECT is_manual_sort FROM manual_sort_folders
                        WHERE folder_id = ?
                    """,
                        (folder_id,),
                    )
                    manual_sort_result = cursor.fetchone()
                    is_manual_sort = manual_sort_result and manual_sort_result[0]

                    if is_manual_sort:
                        # 手动排序文件夹：新图片添加到末尾，不改变现有顺序
                        cursor = conn.execute(
                            """
                            SELECT COALESCE(MAX(order_index), -1)
                            FROM images
                            WHERE folder_id = ?
                        """,
                            (folder_id,),
                        )
                        max_order = cursor.fetchone()[0]

                        for i, file_path in enumerate(new_images):
                            try:
                                name = Path(file_path).stem
                                order_index = max_order + 1 + i  # 添加到末尾

                                conn.execute(
                                    """
                                    INSERT INTO images (name, path, folder_id, order_index)
                                    VALUES (?, ?, ?, ?)
                                """,
                                    (name, file_path, folder_id, order_index),
                                )

                                progress_bar["value"] = (i + 1) / len(new_images) * 100
                                count_label["text"] = f"{i + 1}/{len(new_images)}"
                                progress.update()
                            except Exception as e:
                                print(f"导入失败: {file_path}, 错误: {e}")
                    else:
                        # 自动排序文件夹：按文件名排序
                        for i, file_path in enumerate(new_images):
                            try:
                                name = Path(file_path).stem
                                order_index = position_map[
                                    file_path
                                ]  # 使用排序后的位置

                                conn.execute(
                                    """
                                    INSERT INTO images (name, path, folder_id, order_index)
                                    VALUES (?, ?, ?, ?)
                                """,
                                    (name, file_path, folder_id, order_index),
                                )

                                progress_bar["value"] = (i + 1) / len(new_images) * 100
                                count_label["text"] = f"{i + 1}/{len(new_images)}"
                                progress.update()
                            except Exception as e:
                                print(f"导入失败: {file_path}, 错误: {e}")

                        # 更新所有已存在图片的order_index以匹配新的排序
                        for file_path in existing_images:
                            order_index = position_map[file_path]
                            conn.execute(
                                """
                                UPDATE images
                                SET order_index = ?
                                WHERE path = ? AND folder_id = ?
                            """,
                                (order_index, file_path, folder_id),
                            )

                    conn.commit()
                    progress.destroy()

                    # 更新项目树
                    self.load_projects()

            except Exception as e:
                print(f"导入失败: {e}")
                messagebox.showerror("错误", f"导入失败: {e}")

    def get_sort_key(self, filename):
        """获取排序键值
        支持多种文件名格式：
        1. "001.圣哉三一1" - 前缀数字.中文后缀数字
        2. "10想起你" - 开头数字+中文
        3. "因为有你01" - 中文数字
        排序优先级：前缀数字 > 中文拼音 > 后缀数字
        """
        import re

        # 移除扩展名
        name = Path(filename).stem

        # 初始化排序键组件
        prefix_number = 0
        text_part = name
        suffix_number = 0

        # 模式1: 匹配 "001.圣哉三一1" 格式（前缀数字.中文后缀数字）
        pattern1 = re.match(r"^(\d+)\.(.+?)(\d+)$", name)
        if pattern1:
            prefix_number = int(pattern1.group(1))
            text_part = pattern1.group(2)
            suffix_number = int(pattern1.group(3))
        else:
            # 模式2: 匹配 "10想起你" 格式（开头数字+中文）
            pattern2 = re.match(r"^(\d+)(.+)$", name)
            if pattern2:
                prefix_number = int(pattern2.group(1))
                text_part = pattern2.group(2)
                suffix_number = 0  # 没有后缀数字
            else:
                # 模式3: 匹配 "因为有你_01" 或 "因为有你01" 格式（中文_数字或中文数字）
                pattern3 = re.search(r"^(.+?)_?(\d+)$", name)
                if pattern3:
                    text_part = pattern3.group(1).replace("_", "")  # 移除下划线
                    suffix_number = int(pattern3.group(2))
                    prefix_number = 0  # 没有前缀数字
                else:
                    # 模式4: 纯文本，没有数字
                    text_part = name
                    prefix_number = 0
                    suffix_number = 0

        # 获取中文的拼音首字母
        pinyin_part = ""
        if text_part:
            pinyin_part = "".join(
                pypinyin.lazy_pinyin(text_part, style=pypinyin.FIRST_LETTER)
            )

        # 排序键：(前缀数字, 中文拼音, 后缀数字)
        return (prefix_number, pinyin_part.lower(), suffix_number)

    def save_yellow_text_image(self):
        """另存黄字反色图片"""
        if not self.image:
            messagebox.showwarning("警告", "请先打开一张图片！")
            return

        try:
            # 生成黄字反色图片
            yellow_image = self.apply_yellow_text_effect(self.image.copy())

            # 获取当前图片的名称和路径信息
            current_image_file = None
            if hasattr(self, "current_path") and self.current_path:
                current_image_file = self.current_path

            if current_image_file:
                original_path = Path(current_image_file)
                suggested_name = original_path.name  # 使用原始图片的完整文件名
                initial_dir = str(original_path.parent)
            else:
                suggested_name = "黄字图片.png"
                initial_dir = str(Path.home())

            # 弹出保存文件对话框
            save_path = filedialog.asksaveasfilename(
                title="另存黄字图片",
                initialfile=suggested_name,
                initialdir=initial_dir,
                filetypes=[
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg"),
                    ("All files", "*.*"),
                ],
            )

            if save_path:
                # 根据文件扩展名确定保存格式
                file_ext = Path(save_path).suffix.lower()
                if file_ext == ".png":
                    yellow_image.save(save_path, "PNG")
                elif file_ext in [".jpg", ".jpeg"]:
                    # JPEG不支持透明度，需要转换为RGB
                    if yellow_image.mode == "RGBA":
                        # 创建白色背景
                        rgb_image = Image.new(
                            "RGB", yellow_image.size, (0, 0, 0)
                        )  # 黑色背景
                        rgb_image.paste(
                            yellow_image, mask=yellow_image.split()[-1]
                        )  # 使用alpha通道作为mask
                        rgb_image.save(save_path, "JPEG", quality=95)
                    else:
                        yellow_image.save(save_path, "JPEG", quality=95)
                else:
                    # 其他格式，默认使用PNG
                    yellow_image.save(save_path, "PNG")

                messagebox.showinfo("成功", f"黄字图片已保存到:\n{save_path}")

        except Exception as e:
            print(f"保存黄字图片失败: {e}")
            messagebox.showerror("错误", f"保存黄字图片失败:\n{str(e)}")

    def clear_current_image(self):
        """清除当前图片和相关状态"""
        # 清除画布
        if self.image_on_canvas:
            self.canvas.delete(self.image_on_canvas)
            self.image_on_canvas = None

        # 清除图片对象
        self.image = None
        self.photo = None

        # 清除缓存
        self.image_cache.clear()

        # 移除图片效果缓存清理（已改为实时处理）

        # 清除关键帧状态
        self.current_image_id = None
        self.current_keyframe_index = -1
        if hasattr(self, "indicator_frame"):
            self.update_keyframe_indicators()

        # 重置当前路径
        self.current_path = None

    def update_image(self):
        """更新图片显示 - 修复与投影屏幕的显示一致性"""
        if not self.image:
            return

        try:
            # 使用统一的尺寸计算方式，确保与投影屏幕一致
            canvas_width, canvas_height = self.get_effective_screen_size(
                is_projection_screen=False
            )

            if self.original_mode:
                # 智能原图模式：根据屏幕分辨率智能缩放，最大化利用屏幕空间
                width_ratio = canvas_width / self.image.width
                height_ratio = canvas_height / self.image.height

                # 选择较小的缩放比例以确保完整显示
                scale_ratio = min(width_ratio, height_ratio)

                # 智能缩放策略：根据图片和屏幕的关系调整
                if scale_ratio < 1:
                    # 图片大于屏幕：缩小到适合屏幕
                    pass  # 使用计算出的scale_ratio
                else:
                    # 图片小于屏幕：智能放大以更好地利用屏幕空间
                    # 根据屏幕分辨率和图片大小动态调整最大放大倍数
                    screen_area = canvas_width * canvas_height
                    image_area = self.image.width * self.image.height
                    area_ratio = screen_area / image_area

                    # 动态计算最大放大倍数
                    if area_ratio > 16:  # 屏幕面积是图片的16倍以上
                        max_scale = 6.0  # 允许更大的放大倍数
                    elif area_ratio > 9:  # 屏幕面积是图片的9倍以上
                        max_scale = 4.0
                    elif area_ratio > 4:  # 屏幕面积是图片的4倍以上
                        max_scale = 3.0
                    else:
                        max_scale = 2.0  # 保守的放大倍数

                    # 应用最大放大限制
                    scale_ratio = min(scale_ratio, max_scale)

                new_width = int(self.image.width * scale_ratio)
                new_height = int(self.image.height * scale_ratio)

            else:
                # 正常模式下的缩放逻辑
                base_ratio = canvas_width / self.image.width
                final_ratio = base_ratio * self.zoom_ratio
                new_width = int(self.image.width * final_ratio)
                new_height = int(self.image.height * final_ratio)

            # 检查普通图片缓存
            cached_photo = None
            cache_key = f"{new_width}x{new_height}_normal"

            # 只对普通模式使用缓存，效果模式直接实时处理
            if not self.is_inverted and not self.is_simple_inverted:
                if cache_key in self.image_cache:
                    cached_photo = self.image_cache[cache_key]

            # 如果没有缓存，生成新的图片
            if cached_photo is None:
                # 使用reducing_gap参数优化resize性能
                reducing_gap = (
                    2.0
                    if min(new_width, new_height)
                    < min(self.image.width, self.image.height)
                    else None
                )

                # 统一的实时处理逻辑
                resized_image = self.image.resize(
                    (new_width, new_height),
                    Image.Resampling.LANCZOS,
                    reducing_gap=reducing_gap,
                )

                # 根据模式应用效果
                if self.is_inverted:
                    resized_image = self.apply_yellow_text_effect(resized_image)
                elif self.is_simple_inverted:
                    resized_image = self.simple_invert_image(resized_image)

                # 创建PhotoImage并缓存
                cached_photo = ImageTk.PhotoImage(resized_image)

                # 只缓存普通模式的图片，效果模式不缓存（避免内存浪费）
                if not self.is_inverted and not self.is_simple_inverted:
                    cache_key = f"{new_width}x{new_height}_normal"
                    self.image_cache[cache_key] = cached_photo

            # 使用缓存的图片
            self.photo = cached_photo

            # 清除旧图片
            if self.image_on_canvas:
                self.canvas.delete(self.image_on_canvas)

            # 计算居中位置 - 水平和垂直都居中
            x = max(0, (canvas_width - new_width) // 2)
            y = max(0, (canvas_height - new_height) // 2)

            # 显示新图片 - 在原图模式下居中显示
            if self.original_mode:
                self.image_on_canvas = self.canvas.create_image(
                    x, y, anchor=tk.NW, image=self.photo
                )
                # 在原图模式下，如果图片完全适合屏幕，则不需要滚动
                if new_height <= canvas_height:
                    self.canvas.configure(
                        scrollregion=(0, 0, canvas_width, canvas_height)
                    )
                else:
                    # 如果图片高度超过屏幕，允许滚动，但图片保持居中
                    scroll_height = new_height + canvas_height
                    self.canvas.configure(
                        scrollregion=(0, 0, canvas_width, scroll_height)
                    )
            else:
                # 正常模式保持原有逻辑
                self.image_on_canvas = self.canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.photo
                )
                # 设置滚动区域 - 允许将底部内容拉到顶部显示（与投影屏幕保持一致）
                if new_height > canvas_height:
                    scroll_height = (
                        new_height + canvas_height
                    )  # 图片高度 + 一个屏幕高度的额外空间
                else:
                    scroll_height = canvas_height
                self.canvas.configure(scrollregion=(0, 0, canvas_width, scroll_height))

            # 立即更新画布
            self.canvas.update_idletasks()

            # 更新关键帧指示器
            self.update_keyframe_indicators()

            # 更新预览线
            self.update_preview_lines()

        except Exception as e:
            print(f"更新图片失败: {e}")

    def update_image_quality(self, width, height):
        """质量图片更新"""
        if not self.is_scrolling:
            cache_key = f"{width}x{height}_{'inverted' if self.is_inverted else 'normal'}_{'simple_inverted' if self.is_simple_inverted else 'normal'}"

            if cache_key not in self.image_cache:
                # 统一的实时处理逻辑
                resized_image = self.image.resize(
                    (width, height), Image.Resampling.LANCZOS
                )

                # 根据模式应用效果
                if self.is_inverted:
                    resized_image = self.apply_yellow_text_effect(resized_image)
                elif self.is_simple_inverted:
                    resized_image = self.simple_invert_image(resized_image)

                self.image_cache[cache_key] = ImageTk.PhotoImage(resized_image)

            if self.image_on_canvas:
                self.canvas.itemconfig(
                    self.image_on_canvas, image=self.image_cache[cache_key]
                )

    def on_scroll(self, *args):
        """优化的滚动处理"""
        current_time = time.time() * 1000
        self.is_scrolling = True

        # 取消之前的定时器
        if self.scroll_timer:
            self.root.after_cancel(self.scroll_timer)

        # 设置新的定时器
        self.scroll_timer = self.root.after(100, self.on_scroll_end)

        # 主屏幕滚动
        self.canvas.yview(*args)

        # 如果是反色模式或普通反色模式，确保滚动时保持反色效果
        if (self.is_inverted or self.is_simple_inverted) and self.image:
            # 使用统一的尺寸计算方式
            canvas_width, canvas_height = self.get_effective_screen_size(
                is_projection_screen=False
            )
            if self.original_mode:
                # 智能原图模式：根据屏幕分辨率智能缩放，最大化利用屏幕空间
                width_ratio = canvas_width / self.image.width
                height_ratio = canvas_height / self.image.height
                scale_ratio = min(width_ratio, height_ratio)

                # 智能缩放策略：根据图片和屏幕的关系调整
                if scale_ratio < 1:
                    # 图片大于屏幕：缩小到适合屏幕
                    pass  # 使用计算出的scale_ratio
                else:
                    # 图片小于屏幕：智能放大以更好地利用屏幕空间
                    screen_area = canvas_width * canvas_height
                    image_area = self.image.width * self.image.height
                    area_ratio = screen_area / image_area

                    # 动态计算最大放大倍数
                    if area_ratio > 16:
                        max_scale = 6.0
                    elif area_ratio > 9:
                        max_scale = 4.0
                    elif area_ratio > 4:
                        max_scale = 3.0
                    else:
                        max_scale = 2.0

                    scale_ratio = min(scale_ratio, max_scale)

                new_width = int(self.image.width * scale_ratio)
                new_height = int(self.image.height * scale_ratio)
            else:
                base_ratio = canvas_width / self.image.width
                final_ratio = base_ratio * self.zoom_ratio
                new_width = int(self.image.width * final_ratio)
                new_height = int(self.image.height * final_ratio)

            cache_key = f"{new_width}x{new_height}_{'inverted' if self.is_inverted else 'normal'}_{'simple_inverted' if self.is_simple_inverted else 'normal'}"

            if cache_key not in self.image_cache:
                # 统一的实时处理逻辑
                resized_image = self.image.resize(
                    (new_width, new_height), Image.Resampling.LANCZOS
                )

                # 根据模式应用效果
                if self.is_inverted:
                    resized_image = self.apply_yellow_text_effect(resized_image)
                elif self.is_simple_inverted:
                    resized_image = self.simple_invert_image(resized_image)
                self.image_cache[cache_key] = ImageTk.PhotoImage(resized_image)

            if self.image_on_canvas:
                self.canvas.itemconfig(
                    self.image_on_canvas, image=self.image_cache[cache_key]
                )

        # 降低同步率，使用绝对位置同步
        if self.second_canvas and self.sync_enabled:
            self.sync_projection_screen_absolute()
            self.last_scroll_time = current_time

        # 更新预览线
        self.update_preview_lines()

    def on_scroll_end(self):
        """滚动结束处理"""
        self.is_scrolling = False
        self.scroll_timer = None

        # 更新高质量图片
        if self.image:
            # 使用统一的尺寸计算方式
            canvas_width, canvas_height = self.get_effective_screen_size(
                is_projection_screen=False
            )
            if self.original_mode:
                # 智能原图模式：根据屏幕分辨率智能缩放，最大化利用屏幕空间
                width_ratio = canvas_width / self.image.width
                height_ratio = canvas_height / self.image.height
                scale_ratio = min(width_ratio, height_ratio)

                # 智能缩放策略：根据图片和屏幕的关系调整
                if scale_ratio < 1:
                    # 图片大于屏幕：缩小到适合屏幕
                    pass  # 使用计算出的scale_ratio
                else:
                    # 图片小于屏幕：智能放大以更好地利用屏幕空间
                    screen_area = canvas_width * canvas_height
                    image_area = self.image.width * self.image.height
                    area_ratio = screen_area / image_area

                    # 动态计算最大放大倍数
                    if area_ratio > 16:
                        max_scale = 6.0
                    elif area_ratio > 9:
                        max_scale = 4.0
                    elif area_ratio > 4:
                        max_scale = 3.0
                    else:
                        max_scale = 2.0

                    scale_ratio = min(scale_ratio, max_scale)

                new_width = int(self.image.width * scale_ratio)
                new_height = int(self.image.height * scale_ratio)
            else:
                base_ratio = canvas_width / self.image.width
                final_ratio = base_ratio * self.zoom_ratio
                new_width = int(self.image.width * final_ratio)
                new_height = int(self.image.height * final_ratio)

            self.update_image_quality(new_width, new_height)

    def toggle_projection(self):
        """切换投影显示状态"""
        if self.second_window:
            # 关闭投影窗口
            if self.second_window:
                self.second_window.destroy()
                self.second_window = None
                self.second_canvas = None
                self.btn_show.configure(bg="#f0f0f0", text="投影")  # 恢复原色和原文本
                self.sync_enabled = False

                # 关闭投影时清理全局热键
                self.cleanup_global_hotkeys()
                self.global_hotkeys_enabled = False
                print("投影已关闭，全局热键已禁用")

        else:
            # 打开投影窗口
            self.open_projection()

    def open_projection(self):
        """打开投影窗口"""
        if not self.image:
            messagebox.showwarning("警告", "请先打开一张图片！")
            return

        if len(self.screens) > 1:
            self.show_on_second_screen()
            self.btn_show.configure(bg="#90EE90", text="结束")

            # 开启投影后启用全局热键
            if not self.global_hotkeys_enabled:
                self.setup_global_hotkeys()
                self.global_hotkeys_enabled = True
                print("投影已开启，全局热键已启用")

    def show_on_second_screen(self):
        """显示到选定的屏幕"""
        if not self.image:
            messagebox.showwarning("警告", "请先打开一张图片！")

        if len(self.screens) >= 1:
            # 获取选定的屏幕
            selected = self.screen_combo.current()
            screen = self.screens[selected]

            # 检查是否是主显示器
            if hasattr(screen, "is_primary") and screen.is_primary:
                messagebox.showwarning("警告", "不能投影到主显示器！")
                return

            if self.second_window:
                self.second_window.destroy()

            # 创建新窗口
            self.second_window = tk.Toplevel()
            self.second_window.title("投影")
            self.second_window.overrideredirect(True)

            # 创建画布
            self.second_canvas = tk.Canvas(
                self.second_window, bg="black", highlightthickness=0
            )
            self.second_canvas.pack(fill=tk.BOTH, expand=True)

            # 使用统一的有效显示区域计算（与主屏幕保持一致）
            screen_width, screen_height = self.get_effective_screen_size(
                is_projection_screen=True
            )

            if self.original_mode:
                # 智能原图模式：根据屏幕分辨率智能缩放，最大化利用屏幕空间
                width_ratio = screen_width / self.image.width
                height_ratio = screen_height / self.image.height

                # 选择较小的缩放比例以确保完整显示
                scale_ratio = min(width_ratio, height_ratio)

                # 计算面积比（用于日志输出）
                screen_area = screen_width * screen_height
                image_area = self.image.width * self.image.height
                area_ratio = screen_area / image_area

                # 智能缩放策略：根据图片和屏幕的关系调整
                if scale_ratio < 1:
                    # 图片大于屏幕：缩小到适合屏幕
                    pass  # 使用计算出的scale_ratio
                else:
                    # 图片小于屏幕：智能放大以更好地利用屏幕空间
                    # 根据屏幕分辨率和图片大小动态调整最大放大倍数

                    # 动态计算最大放大倍数
                    if area_ratio > 16:  # 屏幕面积是图片的16倍以上
                        max_scale = 6.0  # 允许更大的放大倍数
                    elif area_ratio > 9:  # 屏幕面积是图片的9倍以上
                        max_scale = 4.0
                    elif area_ratio > 4:  # 屏幕面积是图片的4倍以上
                        max_scale = 3.0
                    else:
                        max_scale = 2.0  # 保守的放大倍数

                    # 应用最大放大限制
                    scale_ratio = min(scale_ratio, max_scale)

                new_width = int(self.image.width * scale_ratio)
                new_height = int(self.image.height * scale_ratio)

                print(
                    f"投影智能原图模式: 原尺寸({self.image.width}x{self.image.height}) -> 显示尺寸({new_width}x{new_height}) 缩放比例: {scale_ratio:.2f} 面积比: {area_ratio:.1f}"
                )
            else:
                # 正常模式下的缩放逻辑
                base_ratio = screen_width / self.image.width
                final_ratio = base_ratio * self.zoom_ratio
                new_width = int(self.image.width * final_ratio)
                new_height = int(self.image.height * final_ratio)

            # 统一的实时处理逻辑
            resized_image = self.image.resize(
                (new_width, new_height), Image.Resampling.LANCZOS
            )

            # 根据模式应用效果
            if self.is_inverted:
                resized_image = self.apply_yellow_text_effect(resized_image)
            elif self.is_simple_inverted:
                resized_image = self.simple_invert_image(resized_image)
            self.second_photo = ImageTk.PhotoImage(resized_image)

            # 计算居中位置 - 水平和垂直都居中
            x = max(0, (screen_width - new_width) // 2)
            y = max(0, (screen_height - new_height) // 2)

            # 显示新图片 - 在原图模式下居中显示
            if self.original_mode:
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, y, anchor=tk.NW, image=self.second_photo
                )
            else:
                # 正常模式保持原有逻辑（顶部显示）
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.second_photo
                )

            # 设置投影窗口的滚动区域
            if self.original_mode:
                # 在原图模式下，如果图片完全适合屏幕，则不需要滚动
                if new_height <= screen_height:
                    self.second_canvas.configure(
                        scrollregion=(0, 0, screen_width, screen_height)
                    )
                else:
                    # 如果图片高度超过屏幕，允许滚动，但图片保持居中
                    scroll_height = new_height + screen_height
                    self.second_canvas.configure(
                        scrollregion=(0, 0, screen_width, scroll_height)
                    )
            else:
                # 正常模式保持原有逻辑 - 允许将底部内容拉到顶部显示
                if new_height > screen_height:
                    scroll_height = (
                        new_height + screen_height
                    )  # 图片高度 + 一个屏幕高度的额外空间
                else:
                    scroll_height = screen_height
                self.second_canvas.configure(
                    scrollregion=(0, 0, screen_width, scroll_height)
                )

            # 设置窗口位置和大小到选定的屏幕
            self.second_window.geometry(
                f"{screen.width}x{screen.height}+{screen.x}+{screen.y}"
            )

            # 设置副屏更新
            self.second_window.after(40, self.second_window.update)

            # 绑定事件
            self.second_window.bind("<Escape>", lambda e: self.second_window.destroy())

            # 首次打开时开启同步
            self.sync_enabled = True

    def on_mousewheel(self, event):
        """鼠标滚轮事件处理"""
        if self.image:
            # 计算滚动量
            delta = -1 * (event.delta // 120)
            # 主屏幕滚动
            self.canvas.yview_scroll(delta, "units")
            # 只在同步开启时更新副屏，使用绝对位置同步
            if self.second_canvas and self.sync_enabled:
                self.sync_projection_screen_absolute()
                self.second_window.update()

            # 添加预览线更新
            self.update_preview_lines()

    def reset_view(self):
        """返回顶部"""
        # 主画布返回顶部
        self.canvas.yview_moveto(0)
        # 只在同步开启时更副屏，使用绝对位置同步
        if self.second_canvas and self.sync_enabled:
            self.sync_projection_screen_absolute()
            self.second_window.update()

    def zoom_in(self):
        """放大"""
        if self.image:
            self.zoom_ratio = min(2.0, self.zoom_ratio * self.zoom_step)
            self.update_image()
            # 只在同步开启时更新副屏
            if self.second_window and self.sync_enabled:
                self.update_second_screen()

    def zoom_out(self):
        """缩小"""
        if self.image:
            self.zoom_ratio = max(0.5, self.zoom_ratio * (1 / self.zoom_step))
            self.update_image()
            # 只在同步开启时更新副屏
            if self.second_window and self.sync_enabled:
                self.update_second_screen()

    def update_second_screen(self):
        """更新第二屏幕显示"""
        if not self.second_window or not self.image:
            return

        try:
            # 获取选定的屏幕
            selected = self.screen_combo.current()
            screen = self.screens[selected]

            # 获取实际窗口尺寸而不是屏幕尺寸
            screen_width = self.second_window.winfo_width()
            screen_height = self.second_window.winfo_height()

            # 确保获取到有效的窗口尺寸
            if screen_width <= 1 or screen_height <= 1:
                screen_width = screen.width
                screen_height = screen.height
                # 重新设置窗口大小以确保正确显示
                self.second_window.geometry(
                    f"{screen_width}x{screen_height}+{screen.x}+{screen.y}"
                )
                self.second_window.update_idletasks()
                # 再次尝试获取窗口尺寸
                screen_width = self.second_window.winfo_width() or screen.width
                screen_height = self.second_window.winfo_height() or screen.height

            print(f"投影窗口实际尺寸: {screen_width}x{screen_height}")

            if self.original_mode:
                # 智能原图模式：根据屏幕分辨率智能缩放，最大化利用屏幕空间
                width_ratio = screen_width / self.image.width
                height_ratio = screen_height / self.image.height

                # 选择较小的缩放比例以确保完整显示
                scale_ratio = min(width_ratio, height_ratio)

                # 智能缩放策略：根据图片和屏幕的关系调整
                if scale_ratio < 1:
                    # 图片大于屏幕：缩小到适合屏幕
                    pass  # 使用计算出的scale_ratio
                else:
                    # 图片小于屏幕：智能放大以更好地利用屏幕空间
                    # 根据屏幕分辨率和图片大小动态调整最大放大倍数
                    screen_area = screen_width * screen_height
                    image_area = self.image.width * self.image.height
                    area_ratio = screen_area / image_area

                    # 动态计算最大放大倍数
                    if area_ratio > 16:  # 屏幕面积是图片的16倍以上
                        max_scale = 6.0  # 允许更大的放大倍数
                    elif area_ratio > 9:  # 屏幕面积是图片的9倍以上
                        max_scale = 4.0
                    elif area_ratio > 4:  # 屏幕面积是图片的4倍以上
                        max_scale = 3.0
                    else:
                        max_scale = 2.0  # 保守的放大倍数

                    # 应用最大放大限制
                    scale_ratio = min(scale_ratio, max_scale)

                new_width = int(self.image.width * scale_ratio)
                new_height = int(self.image.height * scale_ratio)

                print(
                    f"更新投影智能原图模式: 原尺寸({self.image.width}x{self.image.height}) -> 显示尺寸({new_width}x{new_height}) 缩放比例: {scale_ratio:.2f} 面积比: {area_ratio:.1f}"
                )
            else:
                # 正常模式下的缩放逻辑保持不变
                base_ratio = screen_width / self.image.width
                final_ratio = base_ratio * self.zoom_ratio
                new_width = int(self.image.width * final_ratio)
                new_height = int(self.image.height * final_ratio)

            # 统一的实时处理逻辑
            resized_image = self.image.resize(
                (new_width, new_height), Image.Resampling.LANCZOS
            )

            # 根据模式应用效果
            if self.is_inverted:
                resized_image = self.apply_yellow_text_effect(resized_image)
            elif self.is_simple_inverted:
                resized_image = self.simple_invert_image(resized_image)
            self.second_photo = ImageTk.PhotoImage(resized_image)

            # 将图片放在顶部，水平居中
            x = max(0, (screen_width - new_width) // 2)

            # 清除所有现有内容
            self.second_canvas.delete("all")

            # 显示新图片
            self.second_image_on_canvas = self.second_canvas.create_image(
                x, 0, anchor=tk.NW, image=self.second_photo
            )

            # 设置投影窗口滚动区域 - 允许将底部内容拉到顶部显示
            if new_height > screen_height:
                scroll_height = (
                    new_height + screen_height
                )  # 图片高度 + 一个屏幕高度的额外空间
            else:
                scroll_height = screen_height

            self.second_canvas.configure(
                scrollregion=(0, 0, screen_width, scroll_height)
            )

            # 立即更新画布
            self.second_canvas.update_idletasks()

        except Exception as e:
            print(f"更新投影失败: {e}")

    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
                    self.zoom_ratio = config.get("zoom_ratio", 1.0)
                    self.zoom_step = config.get("zoom_step", 1.1)
                    # 先加载自定义黄字颜色
                    for item in config.get("custom_yellow_colors", []):
                        if item["name"] not in self.yellow_text_presets:
                            self.yellow_text_presets[item["name"]] = {
                                "r": item["r"],
                                "g": item["g"],
                                "b": item["b"],
                            }
                    # 再设置当前颜色
                    saved_color_name = config.get("yellow_color_name")
                    if (
                        saved_color_name
                        and saved_color_name in self.yellow_text_presets
                    ):
                        self.current_yellow_color_name = saved_color_name
                        self.yellow_text_rgb = dict(
                            self.yellow_text_presets[saved_color_name]
                        )
                    else:
                        self.current_yellow_color_name = list(
                            self.yellow_text_presets.keys()
                        )[0]
                        self.yellow_text_rgb = dict(
                            self.yellow_text_presets[self.current_yellow_color_name]
                        )
                    # 批量加载项目列表
                    self.batch_load_projects(config.get("projects", []))
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.zoom_ratio = 1.0
            self.zoom_step = 1.1
            self.current_yellow_color_name = list(self.yellow_text_presets.keys())[0]
            self.yellow_text_rgb = dict(
                self.yellow_text_presets[self.current_yellow_color_name]
            )
        # 确保颜色设置已初始化
        if self.current_yellow_color_name is None:
            self.current_yellow_color_name = list(self.yellow_text_presets.keys())[0]
            self.yellow_text_rgb = dict(
                self.yellow_text_presets[self.current_yellow_color_name]
            )

    def batch_load_projects(self, saved_items):
        """批量加载项目"""
        # 预先清空形图
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        self.image_items.clear()

        # 批量插入项目
        for item in saved_items:
            try:
                abs_path = Path(item["path"]).resolve()
                if abs_path.exists():
                    # 添加到项目表
                    project_item = {
                        "id": item["id"],
                        "name": item["name"],
                        "path": abs_path,
                    }
                    self.image_items.append(project_item)

                    item_text = f"{project_item['id']}. {project_item['name']}"
                    self.project_tree.insert(
                        "", "end", text=item_text, iid=str(project_item["id"])
                    )
            except Exception as e:
                print(f"加载项目失败: {item.get('name', '未知')}, 错误: {e}")

        # 批量更新强制刷新界面
        self.project_tree.update_idletasks()

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                "zoom_ratio": self.zoom_ratio,
                "zoom_step": self.zoom_step,
                "yellow_color_name": self.current_yellow_color_name,
            }
            # 重新收集当前所有自定义颜色（排除预设）
            preset_names = set(
                ["默认", "纯黄", "秋麒麟", "晒黑", "结实的树", "马鞍棕色", "沙棕色"]
            )
            custom_colors = [
                {"name": name, "r": rgb["r"], "g": rgb["g"], "b": rgb["b"]}
                for name, rgb in self.yellow_text_presets.items()
                if name not in preset_names
            ]
            config["custom_yellow_colors"] = custom_colors
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def reset_zoom(self):
        """重置图片原始大小"""
        if self.image:
            self.zoom_ratio = 1.0
            self.update_image()
            # 只在同步开启时更新副屏
            if self.second_window and self.sync_enabled:
                self.update_second_screen()

    def handle_drop(self, files):
        """修改拖放处理方法，支持批量拖拽添加到项目列表"""
        try:
            valid_files = []
            invalid_files = []

            for file_path in files:
                # 将字符串转换为字符串
                if isinstance(file_path, bytes):
                    file_path = file_path.decode("gbk")  # Windows 中文系统使用 GBK 编

                # 检查文件类型
                if file_path.lower().endswith(
                    (".jpg", ".jpeg", ".png", ".bmp", ".gi", ".ti")
                ):
                    valid_files.append(file_path)
                else:
                    invalid_files.append(file_path)

            # 批量添加有效的图片文件
            if valid_files:
                # 批量添加，除了最后一个文件，其他都不立即更新列表
                for i, file_path in enumerate(valid_files[:-1]):
                    self.root.after(
                        10 + i * 5,
                        lambda f=file_path: self.add_image_to_project(
                            f, update_list=False
                        ),
                    )

                # 最后一个文件添加完成后更新项目列表
                if valid_files:
                    last_file = valid_files[-1]
                    delay = 10 + (len(valid_files) - 1) * 5
                    self.root.after(
                        delay,
                        lambda f=last_file: self.add_image_to_project(
                            f, update_list=True
                        ),
                    )

            # 如果有无效文件，显示错误信息
            if invalid_files:
                invalid_count = len(invalid_files)
                valid_count = len(valid_files)
                if valid_count > 0:
                    message = f"成功添加 {valid_count} 个图片文件，{invalid_count} 个文件格式不支持"
                else:
                    message = "所选文件格式不支持，请选择图片文件"
                self.root.after(10, lambda: messagebox.showwarning("提示", message))

        except Exception as e:
            print(f"拖放处理错误: {e}")

    def add_image_to_project(self, file_path, update_list=True):
        """添加图片到项目列表（用于拖拽功能）"""
        try:
            # 获取文件名作为项目名称
            name = Path(file_path).stem
            with sqlite3.connect(self.db_path) as conn:
                # 检查图片是否已存在于根目录（folder_id IS NULL）
                cursor = conn.execute(
                    "SELECT id FROM images WHERE path = ? AND folder_id IS NULL",
                    (file_path,),
                )
                existing = cursor.fetchone()
                if existing:
                    # 如果图片已作为独立图片存在于根目录，不需要重复添加
                    print(f"图片 {name} 已在根目录中")
                    return

                # 检查图片是否存在于某个文件夹中
                cursor = conn.execute(
                    "SELECT id, folder_id FROM images WHERE path = ? AND folder_id IS NOT NULL",
                    (file_path,),
                )
                folder_existing = cursor.fetchone()
                if folder_existing:
                    # 如果图片存在于文件夹中，为拖拽创建一个带标识的虚拟路径
                    print(f"图片 {name} 已存在于文件夹中，将其作为独立项目添加")

                    # 创建一个唯一的虚拟路径，添加简单标识确保唯一性
                    import time

                    timestamp = int(time.time() * 1000)  # 毫秒时间戳
                    virtual_path = f"{file_path}#root_{timestamp}"

                    # 获取当前最大的order_index
                    cursor = conn.execute(
                        "SELECT COALESCE(MAX(order_index), -1) FROM images WHERE folder_id IS NULL"
                    )
                    max_order = cursor.fetchone()[0]

                    # 添加新记录，使用虚拟路径
                    cursor = conn.execute(
                        """
                        INSERT INTO images (name, path, folder_id, order_index)
                        VALUES (?, ?, NULL, ?)
                    """,
                        (name, virtual_path, max_order + 1),
                    )

                    conn.commit()

                    # 根据参数决定是否立即更新项目列表
                    if update_list:
                        self.load_projects()
                    return

                # 如果图片完全不存在，则添加新记录
                # 获取当前最大的order_index
                cursor = conn.execute(
                    "SELECT COALESCE(MAX(order_index), -1) FROM images WHERE folder_id IS NULL"
                )
                max_order = cursor.fetchone()[0]

                # 添加图片（不关联文件夹）
                cursor = conn.execute(
                    """
                    INSERT INTO images (name, path, folder_id, order_index)
                    VALUES (?, ?, NULL, ?)
                """,
                    (name, file_path, max_order + 1),
                )

                conn.commit()

                # 根据参数决定是否立即更新项目列表
                if update_list:
                    self.load_projects()

        except Exception as e:
            print(f"添加图片到项目失败: {e}")

    def _select_image_in_tree(self, image_id):
        """安全地在项目树中选中图片"""
        try:
            # 检查项目是否存在于树中
            if self.project_tree.exists(image_id):
                self.project_tree.selection_set(image_id)
                self.project_tree.see(image_id)
            else:
                print(f"项目树中未找到图片ID: {image_id}")
        except Exception as e:
            print(f"选中图片失败: {e}")

    def update_projection(self):
        """更新副屏图片"""
        if self.second_window and self.image:
            try:
                screen = self.screens[self.screen_combo.current()]
                screen_width = screen.width
                screen_height = screen.height

                if self.original_mode:
                    # 原图模式下需要判断是否需要缩放以适应屏幕
                    width_ratio = screen_width / self.image.width
                    height_ratio = screen_height / self.image.height

                    if width_ratio < 1 or height_ratio < 1:
                        scale_ratio = min(width_ratio, height_ratio)
                        new_width = int(self.image.width * scale_ratio)
                        new_height = int(self.image.height * scale_ratio)
                    else:
                        # 如果图片小于屏幕,使用原始尺寸
                        new_width = self.image.width
                        new_height = self.image.height
                else:
                    # 正常模式下的缩放逻辑保持不变
                    base_ratio = screen_width / self.image.width
                    final_ratio = base_ratio * self.zoom_ratio
                    new_width = int(self.image.width * final_ratio)
                    new_height = int(self.image.height * final_ratio)

                # 统一的实时处理逻辑
                resized_image = self.image.resize(
                    (new_width, new_height), Image.Resampling.LANCZOS
                )

                # 根据模式应用效果
                if self.is_inverted:
                    resized_image = self.apply_yellow_text_effect(resized_image)
                elif self.is_simple_inverted:
                    resized_image = self.simple_invert_image(resized_image)

                self.second_photo = ImageTk.PhotoImage(resized_image)

                # 将图片放在顶部，水平居中 - 与show_on_second_screen保持一致
                x = max(0, (screen_width - new_width) // 2)

                # 如果已有图片，删除旧的
                if hasattr(self, "second_image_on_canvas"):
                    self.second_canvas.delete(self.second_image_on_canvas)

                # 显示新图片 - 使用相同的锚点
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.second_photo
                )

                # 设置投影滚动区域 - 允许将底部内容拉到顶部显示
                if new_height > screen_height:
                    scroll_height = (
                        new_height + screen_height
                    )  # 图片高度 + 一个屏幕高度的额外空间
                else:
                    scroll_height = screen_height

                self.second_canvas.configure(
                    scrollregion=(0, 0, screen_width, scroll_height)
                )

                # 立即更新画布
                self.second_canvas.update_idletasks()

            except Exception as e:
                print(f"更新投影失败: {e}")

    def update_screen_list(self):
        """更新屏幕列表"""
        screen_list = []
        for i, screen in enumerate(self.screens):

            is_primary = screen.x == 0 and screen.y == 0
            screen.is_primary = is_primary

            display_name = str(i + 1)
            if is_primary:
                display_name += "主"
            screen_list.append(display_name)

        if not screen_list:
            screen_list = ["1"]

        self.screen_combo["values"] = screen_list
        # 默认选择第一个非主显示器
        for i, screen in enumerate(self.screens):
            if not screen.is_primary:
                self.screen_combo.current(i)
                break

    def create_project_list(self):
        """创建项目列表"""
        # 创建左侧面板的框架
        self.project_frame = ttk.Frame(self.left_frame)
        self.project_frame.pack(fill=tk.BOTH, expand=True)

        # 添加搜索框
        self.search_frame = ttk.Frame(self.project_frame)
        self.search_frame.pack(fill=tk.X, padx=5, pady=5)

        # 添加搜索图标
        self.search_icon = ttk.Label(
            self.search_frame, text="🔍", style="Search.TLabel"
        )
        self.search_icon.pack(side=tk.LEFT, padx=(0, 5))

        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(
            self.search_frame, textvariable=self.search_var, style="Search.TEntry"
        )
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 添加搜索范围选择
        self.search_scope_frame = ttk.Frame(self.project_frame)
        self.search_scope_frame.pack(fill=tk.X, padx=5, pady=2)

        self.search_scope_var = tk.StringVar(value="全部")
        self.search_scope_label = ttk.Label(
            self.search_scope_frame, text="搜索范围:", style="Search.TLabel"
        )
        self.search_scope_label.pack(side=tk.LEFT)

        self.search_scope_combo = ttk.Combobox(
            self.search_scope_frame,
            textvariable=self.search_scope_var,
            state="readonly",
            width=15,
            style="Search.TCombobox",
        )
        self.search_scope_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 强制设置下拉框字体
        try:
            self.search_scope_combo.configure(
                font=(self.default_font, self.menu_font_size)
            )
        except Exception:
            pass

        # 立即应用字体设置到搜索组件
        self.apply_search_fonts()

        # 初始化搜索范围下拉框
        self.update_search_scope_options()

        # 绑定回车键搜索
        self.search_entry.bind("<Return>", lambda e: self.search_projects())

        # 绑定双击事件，自动清空搜索框内容
        self.search_entry.bind("<Double-Button-1>", self.clear_search_on_double_click)

        # 绑定搜索框内容变化事件，实现实时搜索
        self.search_var.trace_add(
            "write", lambda name, index, mode: self.search_projects()
        )

        # 创建项目树
        self.project_tree = ttk.Treeview(self.project_frame, selectmode="browse")
        self.project_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加滚动条
        self.project_scrollbar = ttk.Scrollbar(
            self.project_frame, orient="vertical", command=self.project_tree.yview
        )
        self.project_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.project_tree.configure(yscrollcommand=self.project_scrollbar.set)

        # 设置列
        self.project_tree["columns"] = "name"
        self.project_tree.column("#0", width=0, stretch=tk.NO)  # 隐藏图标列
        self.project_tree.column("name", width=250, anchor=tk.W)  # 增加名称列宽度

        # 设置表头
        self.project_tree.heading("#0", text="")
        self.project_tree.heading("name", text="项目")

        # 立即应用支持emoji的字体样式
        self.apply_project_tree_fonts()

        # 绑定选择事件
        self.project_tree.bind("<<TreeviewSelect>>", self.on_project_select)

        # 绑定右键菜单
        self.project_tree.bind("<Button-3>", self.show_context_menu)

        # 创建右键菜单（完全动态构建）
        self.context_menu = tk.Menu(self.root, tearoff=0)
        # 所有菜单项将在show_context_menu中根据项目类型动态添加

        # 初始化拖放数据
        self.drag_data = {"item": None, "x": 0, "y": 0, "target": None}

        # 绑定拖放事件
        self.project_tree.bind("<ButtonPress-1>", self.on_tree_press)
        self.project_tree.bind("<B1-Motion>", self.on_tree_motion)
        self.project_tree.bind("<ButtonRelease-1>", self.on_tree_release)

    def update_search_scope_options(self):
        """更新搜索范围下拉框选项"""
        try:
            options = ["全部"]

            # 从数据库获取所有文件夹
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT id, name FROM folders ORDER BY order_index"
                )
                folders = cursor.fetchall()

                for folder_id, folder_name in folders:
                    options.append(f"{folder_name} (ID:{folder_id})")

            # 更新下拉框选项
            self.search_scope_combo["values"] = options

            # 如果当前选择不在选项中，重置为"全部"
            if self.search_scope_var.get() not in options:
                self.search_scope_var.set("全部")

        except Exception as e:
            print(f"更新搜索范围选项失败: {e}")

    def clear_search_on_double_click(self, event):
        """双击搜索框时自动清空内容"""
        try:
            # 清空搜索框内容
            self.search_var.set("")
            # 将光标移到开头
            self.search_entry.icursor(0)
            # 触发搜索以重新加载所有项目
            self.search_projects()
        except Exception as e:
            print(f"清空搜索框失败: {e}")

    def search_projects(self, event=None):
        """搜索项目"""
        search_term = self.search_var.get().lower()
        scope_selection = self.search_scope_var.get()  # 获取选中的搜索范围

        # 清空当前树
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        # 如果搜索词为空，根据范围重新加载
        if not search_term:
            self.load_projects()  # 重新加载所有项目，或者可以根据范围优化，但简单起见先全部加载
            return

        # 解析搜索范围，获取文件夹ID
        search_folder_id = None
        if scope_selection != "全部":
            match = re.search(r"\(ID:(\d+)\)$", scope_selection)
            if match:
                try:
                    search_folder_id = int(match.group(1))
                except ValueError:
                    print(f"无法解析文件夹ID: {scope_selection}")
                    scope_selection = "全部"  # 解析失败则退回搜索全部

        # 搜索数据库
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 1. 搜索文件夹 (仅在搜索范围为 "全部" 时执行)
                if search_folder_id is None:
                    cursor = conn.execute(
                        """
                        SELECT id, name, path, order_index
                        FROM folders
                        WHERE LOWER(name) LIKE ?
                        ORDER BY order_index
                    """,
                        (f"%{search_term}%",),
                    )

                    # 获取手动排序的文件夹列表
                    manual_sort_cursor = conn.execute(
                        """
                        SELECT folder_id FROM manual_sort_folders
                        WHERE is_manual_sort = 1
                    """
                    )
                    manual_sort_folders = {
                        row[0] for row in manual_sort_cursor.fetchall()
                    }

                    folders = cursor.fetchall()
                    for folder_id, name, path, order_index in folders:
                        # 检查是否有原图标记
                        has_mark = self.check_original_mark("folder", folder_id)
                        is_manual_sort = folder_id in manual_sort_folders

                        # 根据标记和排序类型确定图标
                        if has_mark:
                            if is_manual_sort:
                                mark_icon = "★ 🔢 "  # 有标记且手动排序
                            else:
                                mark_icon = "★ "  # 有标记但自动排序
                        else:
                            if is_manual_sort:
                                mark_icon = "☆ 🔢 "  # 无标记但手动排序
                            else:
                                mark_icon = "☆ "  # 无标记且自动排序

                        display_name = mark_icon + name

                        # 使用 project_tree.insert 添加文件夹
                        self.project_tree.insert(
                            "",
                            "end",
                            iid=f"folder_{folder_id}",
                            text="",  # 空的图标列
                            values=(display_name,),  # 图标包含在名称中
                        )

                # 2. 搜索图片 (根据搜索范围调整查询条件)
                if search_folder_id is None:
                    # 范围是 "全部"，搜索所有图片
                    cursor = conn.execute(
                        """
                        SELECT i.id, i.name, i.path, i.order_index, i.folder_id, f.name as folder_name
                        FROM images i
                        LEFT JOIN folders f ON i.folder_id = f.id
                        WHERE LOWER(i.name) LIKE ?
                        ORDER BY i.order_index
                    """,
                        (f"%{search_term}%",),
                    )
                else:
                    # 范围是特定文件夹，仅搜索该文件夹下的图片
                    cursor = conn.execute(
                        """
                        SELECT i.id, i.name, i.path, i.order_index, i.folder_id, f.name as folder_name
                        FROM images i
                        LEFT JOIN folders f ON i.folder_id = f.id
                        WHERE LOWER(i.name) LIKE ? AND i.folder_id = ?
                        ORDER BY i.order_index
                    """,
                        (f"%{search_term}%", search_folder_id),
                    )

                # 获取手动排序的文件夹列表
                manual_sort_cursor = conn.execute(
                    """
                    SELECT folder_id FROM manual_sort_folders
                    WHERE is_manual_sort = 1
                """
                )
                manual_sort_folders = {row[0] for row in manual_sort_cursor.fetchall()}

                images = cursor.fetchall()
                for img_id, name, path, order_index, folder_id, folder_name in images:
                    display_name = name
                    if folder_id is not None and folder_name is not None:
                        display_name = f"{name} ({folder_name})"

                    # 独立图片只显示标记，不显示序号
                    if folder_id is None:
                        has_mark = self.check_original_mark("image", img_id)
                        if has_mark:
                            final_display_name = f"● {display_name}"
                        else:
                            final_display_name = display_name
                    else:
                        # 文件夹内的图片：只有手动排序的文件夹才显示序号
                        if folder_id in manual_sort_folders:
                            # 计算在该文件夹内的序号（order_index + 1）
                            image_number = (
                                (order_index + 1) if order_index is not None else 1
                            )
                            final_display_name = f"{image_number:02d}. {display_name}"
                        else:
                            final_display_name = display_name

                    # 使用 project_tree.insert 添加图片，始终添加到根目录
                    self.project_tree.insert(
                        "",
                        "end",
                        iid=str(img_id),  # 图片ID直接用数字字符串
                        text="",  # 空的图标列
                        values=(final_display_name,),  # 图标包含在名称中
                    )

        except Exception as e:
            print(f"搜索失败: {e}")
            messagebox.showerror("错误", f"搜索失败: {e}")

    def load_projects(self):
        """加载项目列表（优化版本：使用单一连接）"""
        # 清空当前树
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        try:
            # 使用单一连接执行所有查询
            with self.db_manager.get_connection() as conn:
                # 批量获取所有原图标记
                cursor = conn.execute("SELECT item_type, item_id FROM original_marks")
                marks = cursor.fetchall()
                mark_set = (
                    {(item_type, item_id) for item_type, item_id in marks}
                    if marks
                    else set()
                )

                # 获取所有文件夹
                cursor = conn.execute(
                    "SELECT id, name, path FROM folders ORDER BY order_index"
                )
                folders = cursor.fetchall()

                # 获取所有图片
                cursor = conn.execute(
                    """
                    SELECT id, name, path, folder_id, order_index
                    FROM images
                    ORDER BY folder_id, order_index
                """
                )
                images = cursor.fetchall()

                # 获取手动排序的文件夹列表
                manual_sort_cursor = conn.execute(
                    """
                    SELECT folder_id FROM manual_sort_folders
                    WHERE is_manual_sort = 1
                """
                )
                manual_sort_folders = {row[0] for row in manual_sort_cursor.fetchall()}

                # 添加文件夹到树中
                folder_nodes = {}
                if folders:
                    for folder_id, folder_name, folder_path in folders:
                        has_mark = ("folder", folder_id) in mark_set
                        is_manual_sort = folder_id in manual_sort_folders

                        # 根据标记和排序类型确定图标
                        if has_mark:
                            if is_manual_sort:
                                mark_icon = "★ 🔢 "  # 有标记且手动排序
                            else:
                                mark_icon = "★ "  # 有标记但自动排序
                        else:
                            if is_manual_sort:
                                mark_icon = "☆ 🔢 "  # 无标记但手动排序
                            else:
                                mark_icon = "☆ "  # 无标记且自动排序

                        display_name = mark_icon + folder_name

                        folder_node = self.project_tree.insert(
                            "",
                            "end",
                            iid=f"folder_{folder_id}",
                            text="",
                            values=(display_name,),
                        )
                        folder_nodes[folder_id] = folder_node

                # 处理图片
                if images:
                    # 按文件夹分组处理图片
                    current_folder_id = None
                    folder_images = []

                    for (
                        image_id,
                        image_name,
                        image_path,
                        folder_id,
                        order_index,
                    ) in images:
                        if folder_id != current_folder_id:
                            if folder_images:
                                self._process_folder_images(
                                    folder_images, folder_nodes, mark_set
                                )
                            current_folder_id = folder_id
                            folder_images = []
                        folder_images.append(
                            (image_id, image_name, image_path, folder_id, order_index)
                        )

                    if folder_images:
                        self._process_folder_images(
                            folder_images, folder_nodes, mark_set
                        )

        except Exception as e:
            print(f"加载项目列表失败: {e}")
            # 回退到原始方法
            self._load_projects_fallback()

    def _load_projects_fallback(self):
        """回退的项目加载方法（使用原始的多次连接方式）"""
        try:
            # 批量获取所有原图标记
            marks = self.safe_db_execute(
                "SELECT item_type, item_id FROM original_marks", fetch="all"
            )
            mark_set = (
                {(item_type, item_id) for item_type, item_id in marks}
                if marks
                else set()
            )

            # 获取所有文件夹
            folders = self.safe_db_execute(
                "SELECT id, name, path FROM folders ORDER BY order_index", fetch="all"
            )
            if not folders:
                return

            # 获取手动排序的文件夹列表
            manual_sort_folders_result = self.safe_db_execute(
                "SELECT folder_id FROM manual_sort_folders WHERE is_manual_sort = 1",
                fetch="all",
            )
            manual_sort_folders = (
                {row[0] for row in manual_sort_folders_result}
                if manual_sort_folders_result
                else set()
            )

            # 添加文件夹到树中
            folder_nodes = {}
            for folder_id, folder_name, folder_path in folders:
                has_mark = ("folder", folder_id) in mark_set
                is_manual_sort = folder_id in manual_sort_folders

                # 根据标记和排序类型确定图标
                if has_mark:
                    if is_manual_sort:
                        mark_icon = "★ 🔢 "  # 有标记且手动排序
                    else:
                        mark_icon = "★ "  # 有标记但自动排序
                else:
                    if is_manual_sort:
                        mark_icon = "☆ 🔢 "  # 无标记但手动排序
                    else:
                        mark_icon = "☆ "  # 无标记且自动排序

                display_name = mark_icon + folder_name

                folder_node = self.project_tree.insert(
                    "",
                    "end",
                    iid=f"folder_{folder_id}",
                    text="",
                    values=(display_name,),
                )
                folder_nodes[folder_id] = folder_node

            # 获取所有图片
            images = self.safe_db_execute(
                "SELECT id, name, path, folder_id, order_index FROM images ORDER BY folder_id, order_index",
                fetch="all",
            )
            if images:
                # 按文件夹分组处理图片
                current_folder_id = None
                folder_images = []

                for image_id, image_name, image_path, folder_id, order_index in images:
                    if folder_id != current_folder_id:
                        if folder_images:
                            self._process_folder_images(
                                folder_images, folder_nodes, mark_set
                            )
                        current_folder_id = folder_id
                        folder_images = []
                    folder_images.append(
                        (image_id, image_name, image_path, folder_id, order_index)
                    )

                if folder_images:
                    self._process_folder_images(folder_images, folder_nodes, mark_set)

        except Exception as e:
            print(f"回退方法也失败: {e}")

        self.update_search_scope_options()
        self.update_project_tree_marks()

    def _process_folder_images(self, folder_images, folder_nodes, mark_set):
        """处理文件夹内的图片"""
        # 按文件夹分组处理，只有手动排序的文件夹才显示序号
        current_folder_id = None
        folder_image_index = 0
        manual_sort_folders = set()

        # 预先获取所有手动排序的文件夹ID
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT folder_id FROM manual_sort_folders
                    WHERE is_manual_sort = 1
                """
                )
                manual_sort_folders = {row[0] for row in cursor.fetchall()}
        except Exception as e:
            print(f"获取手动排序文件夹失败: {e}")

        for image_id, image_name, image_path, folder_id, order_index in folder_images:
            # 如果切换到新文件夹，重置序号计数
            if folder_id != current_folder_id:
                current_folder_id = folder_id
                folder_image_index = 1
            else:
                folder_image_index += 1

            # 如果图片属于文件夹，添加到对应文件夹节点下
            if folder_id is not None and folder_id in folder_nodes:
                parent = folder_nodes[folder_id]
                # 只有手动排序的文件夹才显示序号
                if folder_id in manual_sort_folders:
                    display_name = f"{folder_image_index:02d}. {image_name}"
                else:
                    display_name = image_name
            else:
                parent = ""
                # 独立图片不显示序号，只显示标记
                has_mark = ("image", image_id) in mark_set
                if has_mark:
                    display_name = f"● {image_name}"
                else:
                    display_name = image_name

            self.project_tree.insert(
                parent, "end", iid=str(image_id), text="", values=(display_name,)
            )

    def on_project_select(self, event):
        """处理项目选择事件"""
        selected_item = self.project_tree.selection()
        if not selected_item:
            return

        item_id = selected_item[0]

        # 如果选中的是文件夹，不加载图片
        if item_id.startswith("folder_"):
            return

        try:
            # 从数据库获取图片信息
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT path, name, order_index FROM images WHERE id = ?
                """,
                    (int(item_id),),
                )
                result = cursor.fetchone()

                if result:
                    path, name, order_index = result

                    # 加载图片(这会触发关键帧的加载)
                    self.load_image(path)

                    # 更新按钮状态（切换图片后更新时间录制相关按钮）
                    self.update_button_status()

                    # 同步播放次数设定到auto_player
                    if self.auto_player and hasattr(self, "target_play_count"):
                        if hasattr(self.auto_player, "target_play_count"):
                            self.auto_player.target_play_count = self.target_play_count

                    # 智能原图模式切换逻辑
                    current_should_use_original = (
                        self.yuantu_manager.should_use_original_mode(int(item_id))
                    )

                    # 记录上一张图片的标记状态
                    if not hasattr(self, "_last_should_use_original"):
                        self._last_should_use_original = False

                    # 根据标记状态智能切换原图模式
                    if current_should_use_original:
                        # 当前图片有标记，开启原图模式
                        if not self.original_mode:
                            self.toggle_original_mode()
                    else:
                        # 当前图片无标记
                        if self._last_should_use_original and self.original_mode:
                            # 从有标记切换到无标记，自动关闭原图模式
                            self.toggle_original_mode()

                    # 更新上一张图片的标记状态
                    self._last_should_use_original = current_should_use_original

        except Exception as e:
            print(f"加载图片失败: {e}")

    def save_project_config(self):
        """保存项目配置到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 更新排序索引
                for i, item in enumerate(self.project_tree.get_children()):
                    item_id = int(item)
                    conn.execute(
                        """
                        UPDATE images
                        SET order_index = ?
                        WHERE id = ?
                    """,
                        (i, item_id),
                    )
                conn.commit()
        except Exception as e:
            print(f"保存项配置失败: {e}")

    def show_context_menu(self, event):
        """显示右键菜单"""
        # 获取点击位置的项目
        item = self.project_tree.identify_row(event.y)

        # 清除之前的所有菜单项
        self.context_menu.delete(0, "end")

        # 如果没有选中任何项目（点击空白区域），显示刷新菜单
        if not item:
            self.context_menu.add_command(
                label="刷新",
                command=self.sync_all_folders,
                font=(self.default_font, self.menu_font_size),
            )
            # 显示菜单
            self.context_menu.post(event.x_root, event.y_root)
            return

        # 选中该项目
        self.project_tree.selection_set(item)

        # 根据项目类型动态构建菜单
        is_folder = item.startswith("folder_")
        try:
            if is_folder:
                # 文件夹：显示删除和原图标记选项
                self.context_menu.add_command(
                    label="删除",
                    command=self.delete_selected_item,
                    font=(self.default_font, self.menu_font_size),
                )
                self.context_menu.add_separator()

                folder_id = int(item.split("_")[1])
                has_mark = self.check_original_mark("folder", folder_id)

                # 添加相应的菜单项
                if has_mark:
                    # 如果已有标记，显示取消标记选项
                    mark_type = self.get_original_mark_type("folder", folder_id)
                    current_mark_text = (
                        "循环原图标记" if mark_type == "loop" else "顺序原图标记"
                    )
                    self.context_menu.add_command(
                        label=f"取消{current_mark_text}",
                        command=self.unmark_original,
                        font=(self.default_font, self.menu_font_size),
                    )
                else:
                    # 如果没有标记，显示两种标记选项
                    self.context_menu.add_command(
                        label="循环原图标记",
                        command=lambda: self.mark_as_original("loop"),
                        font=(self.default_font, self.menu_font_size),
                    )
                    self.context_menu.add_command(
                        label="顺序原图标记",
                        command=lambda: self.mark_as_original("sequence"),
                        font=(self.default_font, self.menu_font_size),
                    )

                # 添加重置排序选项
                self.context_menu.add_separator()
                self.context_menu.add_command(
                    label="重置排序",
                    command=self.reset_folder_sort,
                    font=(self.default_font, self.menu_font_size),
                )
            else:
                # 检查是否在文件夹内
                parent_item = self.project_tree.parent(item)
                if not parent_item:
                    # 独立图片：显示删除和标记选项
                    self.context_menu.add_command(
                        label="删除",
                        command=self.delete_selected_item,
                        font=(self.default_font, self.menu_font_size),
                    )
                    self.context_menu.add_separator()

                    image_id = int(item)
                    has_mark = self.check_original_mark("image", image_id)

                    # 添加相应的菜单项
                    if has_mark:
                        # 如果已有标记，显示取消标记选项
                        mark_type = self.get_original_mark_type("image", image_id)
                        current_mark_text = (
                            "循环原图标记" if mark_type == "loop" else "顺序原图标记"
                        )
                        self.context_menu.add_command(
                            label=f"取消{current_mark_text}",
                            command=self.unmark_original,
                            font=(self.default_font, self.menu_font_size),
                        )
                    else:
                        # 如果没有标记，显示两种标记选项
                        self.context_menu.add_command(
                            label="循环原图标记",
                            command=lambda: self.mark_as_original("loop"),
                            font=(self.default_font, self.menu_font_size),
                        )
                        self.context_menu.add_command(
                            label="顺序原图标记",
                            command=lambda: self.mark_as_original("sequence"),
                            font=(self.default_font, self.menu_font_size),
                        )
                else:
                    # 文件夹内的图片：不显示任何选项，直接返回
                    return

        except Exception as e:
            print(f"更新右键菜单失败: {e}")

        # 显示菜单
        self.context_menu.post(event.x_root, event.y_root)

    def delete_selected_item(self):
        """删除选中的项目"""
        selected = self.project_tree.selection()
        if not selected:
            return

        item_id = selected[0]

        # 判断是文件夹还是图片
        is_folder = item_id.startswith("folder_")

        # 对于图片，检查是否在文件夹内（额外安全检查）
        if not is_folder:
            parent_item = self.project_tree.parent(item_id)
            if parent_item:
                messagebox.showinfo(
                    "提示", "文件夹内的图片不能单独删除，请删除整个文件夹"
                )
                return

        # 确认删除
        if is_folder:
            display_name = self.project_tree.item(item_id, "values")[0]
            # 移除图标前缀获取纯名称
            if display_name.startswith("[●] ") or display_name.startswith("[ ] "):
                folder_name = display_name[4:]
            else:
                folder_name = display_name
            if not messagebox.askyesno(
                "确认删除",
                f"确定要删除文件夹 '{folder_name}' 及其所有图片吗？\n此操作不会删除实际文件。",
            ):
                return
        else:
            display_name = self.project_tree.item(item_id, "values")[0]
            # 检查是否在文件夹内（文件夹内的图片没有图标前缀）
            parent_item = self.project_tree.parent(item_id)
            if parent_item:  # 在文件夹内，直接使用显示名称
                image_name = display_name
            else:  # 独立图片，移除图标前缀（如果有的话）
                if display_name.startswith("● "):
                    image_name = display_name[2:]
                else:
                    image_name = display_name
            if not messagebox.askyesno(
                "确认删除",
                f"确定要删除图片 '{image_name}' 吗？\n此操作不会删除实际文件。",
            ):
                return

        try:
            with sqlite3.connect(self.db_path) as conn:
                if is_folder:
                    # 从文件夹ID字符串中提取数字部分
                    folder_id = int(item_id.split("_")[1])

                    # 删除文件夹下的所有图片
                    conn.execute("DELETE FROM images WHERE folder_id = ?", (folder_id,))

                    # 删除文件夹的原图标记
                    conn.execute(
                        "DELETE FROM original_marks WHERE item_type = ? AND item_id = ?",
                        ("folder", folder_id),
                    )

                    # 删除文件夹
                    conn.execute("DELETE FROM folders WHERE id = ?", (folder_id,))
                else:
                    # 删除图片
                    image_id = int(item_id)

                    # 删除图片的所有关键帧
                    conn.execute(
                        "DELETE FROM keyframes WHERE image_id = ?", (image_id,)
                    )

                    # 删除图片的原图标记
                    conn.execute(
                        "DELETE FROM original_marks WHERE item_type = ? AND item_id = ?",
                        ("image", image_id),
                    )

                    # 删除图片
                    conn.execute("DELETE FROM images WHERE id = ?", (image_id,))

                conn.commit()

            # 从树中删除项目
            self.project_tree.delete(item_id)

            # 如果当前显示的是被删除的图片，清除显示
            if (
                hasattr(self, "current_image_id")
                and str(self.current_image_id) == item_id
            ):
                self.clear_current_image()

        except Exception as e:
            print(f"删除项目失败: {e}")
            messagebox.showerror("错误", f"删除失败: {e}")

    def mark_as_original(self, mark_type="loop"):
        """标记选中项目为原图 - 调用yuantu.py中的方法"""
        self.yuantu_manager.mark_as_original(mark_type)

    def unmark_original(self):
        """取消选中项目的原图标记 - 调用yuantu.py中的方法"""
        self.yuantu_manager.unmark_original()

    def update_project_tree_marks(self):
        """更新项目树中的原图标记显示 - 调用yuantu.py中的方法"""
        self.yuantu_manager.update_project_tree_marks()

    def _update_item_mark_display(self, item_id):
        """更新单个项目的标记显示 - 调用yuantu.py中的方法"""
        self.yuantu_manager._update_item_mark_display(item_id)

    def reorganize_projects(self, conn):
        """重新整理项目ID和序号（跳过手动排序的文件夹）"""
        try:
            # 重新整理非文件夹图片的顺序
            cursor = conn.execute(
                """
                SELECT id FROM images
                WHERE folder_id IS NULL
                ORDER BY order_index
            """
            )
            for index, (id,) in enumerate(cursor.fetchall()):
                conn.execute(
                    "UPDATE images SET order_index = ? WHERE id = ?", (index, id)
                )

            # 重新整理文件夹中图片的顺序（跳过手动排序的文件夹）
            cursor = conn.execute("SELECT id FROM folders")
            for (folder_id,) in cursor.fetchall():
                # 检查是否为手动排序文件夹
                manual_cursor = conn.execute(
                    """
                    SELECT is_manual_sort FROM manual_sort_folders
                    WHERE folder_id = ?
                """,
                    (folder_id,),
                )
                manual_result = manual_cursor.fetchone()
                is_manual_sort = manual_result and manual_result[0]

                if not is_manual_sort:
                    # 只对非手动排序的文件夹重新整理顺序
                    sub_cursor = conn.execute(
                        """
                        SELECT id FROM images
                        WHERE folder_id = ?
                        ORDER BY order_index
                    """,
                        (folder_id,),
                    )
                    for index, (id,) in enumerate(sub_cursor.fetchall()):
                        conn.execute(
                            "UPDATE images SET order_index = ? WHERE id = ?",
                            (index, id),
                        )

            conn.commit()
        except Exception as e:
            print(f"重新整理项目失败: {e}")
            raise e

    def toggle_invert(self):
        """切换反色状态"""
        self.is_inverted = not self.is_inverted

        # 更新按钮状态
        if self.is_inverted:
            self.btn_invert.configure(bg="#90EE90")  # 浅绿色

            # 移除黄字效果预缓存，改为实时处理
        else:
            self.btn_invert.configure(bg="#f0f0f0")  # 恢复默认颜色
            # 移除黄字图片缓存清理（已改为实时处理）

        # 更新图片显示
        self.update_image()

        # 如果投影窗口已打开，更新投影
        if self.second_window and self.sync_enabled:
            self.update_projection()

    def toggle_simple_invert(self):
        """切换普通反色状态"""
        # 如果智能反色已启用，先关闭它
        if self.is_inverted:
            self.is_inverted = False
            self.btn_invert.configure(bg="#f0f0f0")
            # 移除黄字图片缓存清理（已改为实时处理）

        # 切换普通反色状态
        self.is_simple_inverted = not self.is_simple_inverted

        # 更新按钮状态
        if self.is_simple_inverted:
            self.btn_simple_invert.configure(bg="#90EE90")  # 浅绿色，与投屏按钮相同

            # 移除反色效果预缓存，改为实时处理
        else:
            self.btn_simple_invert.configure(bg="#f0f0f0")  # 恢复默认颜色
            # 移除反色图片缓存清理（已改为实时处理）

        # 更新图片显示
        if self.image:
            self.update_image()  # 更新主屏幕
            if self.second_window:
                self.update_second_screen()  # 更新副屏

    def simple_invert_image(self, image):
        """简单反转图片颜色"""
        # 转换为NumPy数组进行快速处理
        img_array = np.array(image)

        # 根据图像模式处理
        if len(img_array.shape) == 3:  # RGB或RGBA图像
            has_alpha = img_array.shape[2] == 4

            if has_alpha:
                # 保存alpha通道
                alpha = img_array[:, :, 3].copy()
                # 反转RGB通道
                inverted = np.zeros_like(img_array)
                inverted[:, :, :3] = 255 - img_array[:, :, :3]
                inverted[:, :, 3] = alpha  # 保持透明度不变
            else:
                # 直接反转所有通道
                inverted = 255 - img_array
        else:  # 灰度图像
            # 直接反转灰度值
            inverted = 255 - img_array

        # 转回PIL图像
        return Image.fromarray(inverted)

    def get_window_screen(self):
        """获取当前窗口所在的屏幕"""
        try:
            import win32gui

            hwnd = self.root.winfo_id()
            monitor = win32gui.MonitorFromWindow(
                hwnd, win32gui.MONITOR_DEFAULTTONEAREST
            )
            info = win32gui.GetMonitorInfo(monitor)

            # 检查是否是主显示器
            is_primary = bool(info["Flags"] & 1)  # MONITORINFOF_PRIMARY = 1

            # 根据监视器信息匹配screens列表中的屏幕
            for screen in self.screens:
                if (
                    screen.x == info["Monitor"][0]
                    and screen.y == info["Monitor"][1]
                    and screen.width == info["Monitor"][2] - info["Monitor"][0]
                    and screen.height == info["Monitor"][3] - info["Monitor"][1]
                ):
                    # 为screen对象添加主显示器标识
                    screen.is_primary = is_primary
                    return screen

        except Exception as e:
            print(f"获取窗口所屏幕失败: {e}")
        return None

    def toggle_original_mode(self):
        """切换原图模式"""
        self.yuantu_manager.toggle_original_mode()

    def on_window_resize(self, event):
        """处理窗口大小改变事件"""
        # 更新图片显示
        self.update_image()

        # 更新关键帧指示器
        if hasattr(self, "indicator_frame"):
            self.update_keyframe_indicators()

    def get_monitor_info(self):
        """获取显示器信息"""
        monitors = []
        for i, monitor in enumerate(screeninfo.get_monitors()):
            if monitor.is_primary:
                name = "主显示器"
            else:
                name = f"显示器{i+1}"
            monitors.append(
                {
                    "name": name,
                    "x": monitor.x,
                    "y": monitor.y,
                    "width": monitor.width,
                    "height": monitor.height,
                    "is_primary": monitor.is_primary,
                }
            )
        return monitors

    def create_monitor_menu(self):
        """创建显示器选择菜单"""
        # 创建一个Frame来容纳标签和下拉框
        monitor_frame = ttk.Frame(self.menu_frame)
        monitor_frame.pack(side=tk.LEFT, padx=5)

        # 创建标签
        label = ttk.Label(monitor_frame, text="显示器:")
        label.pack(side=tk.LEFT)

        self.monitor_var = tk.StringVar()
        monitors = self.get_monitor_info()

        # 创建选择框
        self.monitor_select = ttk.Combobox(
            monitor_frame,  # 改为使用monitor_frame作为父容器
            textvariable=self.monitor_var,
            values=[m["name"] for m in monitors],
            state="readonly",
            width=10,
        )

        # 设置默认值为主显示器
        for i, m in enumerate(monitors):
            if m["is_primary"]:
                self.monitor_select.current(i)
                break

        self.monitor_select.pack(side=tk.LEFT, padx=5)
        self.monitor_select.bind("<<ComboboxSelected>>", self.on_monitor_select)

    def create_preview_lines(self):
        """创建预览线"""
        # 当前关键帧预览线 - 使用实线
        self.preview_line = self.canvas.create_line(
            0, 0, 0, 0, fill="#00FF00", width=3, dash=()  # 初始坐标  #  # 实线
        )

        # 下一个关键帧预览线 - 使用虚线
        self.next_keyframe_line = self.canvas.create_line(
            0,
            0,
            0,
            0,  # 初始坐标
            fill="red",
            width=7,  # 稍细的线条宽度
            dash=(5, 5),  # 虚线样式
        )

        # 初始化更新时间戳
        self._last_preview_update = 0

    def update_preview_lines(self):
        """更新预览线位置 - 仅更新主窗口的预览线"""
        if not hasattr(self, "current_image_id") or not self.preview_line:
            return

        # 添加节流控制
        current_time = time.time()
        if (
            hasattr(self, "_last_update_time")
            and current_time - self._last_update_time < 0.1
        ):
            return
        self._last_update_time = current_time

        try:
            with sqlite3.connect(self.db_path) as conn:
                # 获取所有关键帧的y_position用于绘制预览线,按order_index排序
                cursor = conn.execute(
                    """
                    SELECT id, y_position, order_index
                    FROM keyframes
                    WHERE image_id = ?
                    ORDER BY order_index ASC
                """,
                    (self.current_image_id,),
                )
                keyframes = cursor.fetchall()

                if not keyframes:
                    # 如果没有关键帧，隐藏预览线
                    self.canvas.coords(self.preview_line, 0, 0, 0, 0)
                    self.canvas.coords(self.next_keyframe_line, 0, 0, 0, 0)
                    return

                # 获取当前画布位置
                current_y = self.canvas.canvasy(0) + (self.canvas.winfo_height() / 2)

                # 使用current_keyframe_index来确定当前和下一个关键帧
                if self.current_keyframe_index >= 0:
                    current_frame = keyframes[self.current_keyframe_index]
                    next_frame = keyframes[
                        (self.current_keyframe_index + 1) % len(keyframes)
                    ]
                else:
                    # 找到最接近的关键帧 - 使用y_position进行距离计算
                    current_index = 0
                    min_distance = float("in")
                    for i, frame in enumerate(keyframes):
                        distance = abs(frame[1] - current_y)  # frame[1]是y_position
                        if distance < min_distance:
                            min_distance = distance
                            current_index = i
                    current_frame = keyframes[current_index]
                    next_frame = keyframes[(current_index + 1) % len(keyframes)]

                # 更新主窗口预览线
                canvas_width = self.canvas.winfo_width()
                if current_frame:
                    y = current_frame[1]  # 使用y_position绘制预览线
                    self.canvas.coords(self.preview_line, 0, y, canvas_width, y)
                    self.canvas.tag_raise(self.preview_line)

                if next_frame:
                    y = next_frame[1]  # 使用y_position绘制预览线
                    self.canvas.coords(self.next_keyframe_line, 0, y, canvas_width, y)
                    self.canvas.tag_raise(self.next_keyframe_line)

        except Exception as e:
            print(f"更新预览线失败: {e}")

    def set_initial_pane_position(self):
        """设置初始分隔线位置"""

        def _set_position():
            try:
                # 从数据库读取保存的宽度
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute(
                        "SELECT value FROM ui_settings WHERE key = ?", ("pane_width",)
                    )
                    result = cursor.fetchone()

                    if result:
                        saved_width = int(result[0])
                        self.paned.sashpos(0, saved_width)
                    else:
                        # 使用默认宽度
                        self.paned.sashpos(0, 452)

                self.left_frame.pack_propagate(True)

                # 绑定宽度变化事件
                self.paned.bind("<ButtonRelease-1>", self.on_pane_resize)
                # 绑定拖动事件，实时保存
                self.paned.bind("<B1-Motion>", self.on_pane_resize)

            except Exception as e:
                print(f"设置初始分隔线位置失败: {e}")

        # 使用after方法延迟执行，确保窗口完全加载
        self.root.after(100, _set_position)

    def on_pane_resize(self, event):
        """处理分隔线位置变化事件"""
        try:
            # 获取当前宽度
            current_width = self.paned.sashpos(0)

            # 保存到数据库
            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    """
                    INSERT OR REPLACE INTO ui_settings (key, value)
                    VALUES (?, ?)
                """,
                    ("pane_width", str(current_width)),
                )
                conn.commit()

        except Exception as e:
            print(f"保存分隔线位置失败: {e}")

    def init_dpi_scaling(self):
        """初始化DPI缩放支持"""
        try:
            # 获取系统DPI信息
            import ctypes

            user32 = ctypes.windll.user32
            # 获取主显示器的DPI
            self.dpi = (
                user32.GetDpiForSystem() if hasattr(user32, "GetDpiForSystem") else 96
            )
            self.dpi_factor = self.dpi / 96.0

            # 备用方法：使用tkinter获取缩放因子
            if self.dpi_factor == 1.0:  # 如果上面方法未能获取正确DPI
                scale_factor = self.root.winfo_fpixels("1i") / 72
                self.dpi_factor = scale_factor if scale_factor > 1.0 else 1.0

        except Exception as e:
            print(f"DPI检测失败: {e}")
            self.dpi_factor = 1.0

        # 简化字号设置：菜单栏默认22号
        self.font_size = 22
        self.menu_font_size = int(self.font_size * 0.8)  # 17.6 ≈ 18
        self.small_font_size = int(self.font_size * 0.7)  # 15.4 ≈ 15
        self.large_font_size = int(self.font_size * 1.2)  # 26.4 ≈ 26

    def init_search_styles(self):
        """初始化搜索组件的样式"""
        try:
            style = ttk.Style()

            # 简化padding计算
            base_padding = 6
            label_padding = 6

            # 配置搜索组件的样式
            style.configure(
                "Search.TEntry",
                font=(self.default_font, self.menu_font_size),
                padding=(base_padding, base_padding),
            )
            style.configure(
                "Search.TLabel",
                font=(self.default_font, self.menu_font_size),
                padding=(base_padding, label_padding),
            )
            style.configure(
                "Search.TCombobox",
                font=(self.default_font, self.menu_font_size),
                padding=(base_padding, base_padding),
            )

            # 配置下拉框的箭头按钮样式
            style.configure(
                "Search.TCombobox.TButton",
                font=(self.default_font, self.menu_font_size),
            )

            # 设置下拉框列表字体
            try:
                self.root.option_add(
                    "*TCombobox*Listbox.font", (self.default_font, self.menu_font_size)
                )
                self.root.option_add(
                    "*TEntry*font", (self.default_font, self.menu_font_size)
                )
                self.root.option_add(
                    "*TButton*font", (self.default_font, self.menu_font_size)
                )
                self.root.option_add(
                    "*TLabel*font", (self.default_font, self.menu_font_size)
                )
                self.root.option_add(
                    "*Treeview*font", (self.default_font, self.large_font_size)
                )
                self.root.option_add(
                    "*Treeview.Heading*font", (self.default_font, self.large_font_size)
                )
            except Exception:
                pass
        except Exception as e:
            print(f"初始化搜索样式失败: {e}")

    def apply_search_fonts(self):
        """直接应用字体设置到搜索组件"""
        try:
            # 使用样式系统设置ttk组件的字体
            style = ttk.Style()

            # 简化padding计算
            base_padding = 6
            label_padding = 6

            # 配置搜索组件的样式
            style.configure(
                "Search.TEntry",
                font=(self.default_font, self.menu_font_size),
                padding=(base_padding, base_padding),
            )
            style.configure(
                "Search.TLabel",
                font=(self.default_font, self.menu_font_size),
                padding=(base_padding, label_padding),
            )
            style.configure(
                "Search.TCombobox",
                font=(self.default_font, self.menu_font_size),
                padding=(base_padding, base_padding),
            )

            # 配置下拉框的箭头按钮样式
            style.configure(
                "Search.TCombobox.TButton",
                font=(self.default_font, self.menu_font_size),
            )

            # 应用样式到搜索组件
            if hasattr(self, "search_entry"):
                self.search_entry.configure(style="Search.TEntry")
            if hasattr(self, "search_icon"):
                self.search_icon.configure(style="Search.TLabel")
            if hasattr(self, "search_scope_label"):
                self.search_scope_label.configure(style="Search.TLabel")
            if hasattr(self, "search_scope_combo"):
                self.search_scope_combo.configure(style="Search.TCombobox")

            # 强制刷新下拉框选项字体
            try:
                if hasattr(self, "search_scope_combo"):
                    # 重新设置下拉框选项以应用新字体
                    current_value = self.search_scope_var.get()
                    self.update_search_scope_options()
                    if current_value in self.search_scope_combo["values"]:
                        self.search_scope_var.set(current_value)
            except Exception as e:
                print(f"刷新下拉框选项失败: {e}")

            # 强制刷新组件
            self.root.update_idletasks()
        except Exception as e:
            print(f"应用搜索字体失败: {e}")

    def apply_project_tree_fonts(self):
        """直接应用字体设置到项目树"""
        try:
            style = ttk.Style()
            # 尝试使用支持emoji的字体，按优先级顺序
            font_candidates = [
                "Segoe UI",  # Windows 10/11 默认字体，支持基本emoji
                "Segoe UI Emoji",  # Windows emoji字体
                "Microsoft YaHei UI",  # 原来的字体
                "Arial Unicode MS",  # 备用字体
                "sans-seri",  # 系统默认
            ]

            font_set = False
            for font_name in font_candidates:
                try:
                    # 设置树视图的字体和行高
                    style.configure(
                        "Treeview",
                        font=(font_name, self.large_font_size),
                        rowheight=int(self.font_size * 3),
                    )

                    # 设置树视图表头的字体
                    style.configure(
                        "Treeview.Heading", font=(font_name, self.large_font_size)
                    )

                    font_set = True
                    break
                except Exception:
                    continue

            if not font_set:
                # 如果所有字体都失败，使用默认设置
                style.configure(
                    "Treeview",
                    font=(self.default_font, self.large_font_size),
                    rowheight=int(self.font_size * 3),
                )
                style.configure(
                    "Treeview.Heading", font=(self.default_font, self.large_font_size)
                )

            # 强制刷新项目树
            if hasattr(self, "project_tree"):
                self.project_tree.update_idletasks()

        except Exception as e:
            print(f"应用项目树字体失败: {e}")

    def load_font_settings(self):
        """从数据库加载字体设置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT value FROM ui_settings WHERE key = 'font_size'"
                )
                result = cursor.fetchone()

                if result:
                    self.font_size = int(result[0])
                    self.menu_font_size = int(self.font_size * 0.8)
                    self.small_font_size = int(self.font_size * 0.7)
                    self.large_font_size = int(self.font_size * 1.2)
                else:
                    # 使用默认设置 - 固定为22号
                    self.font_size = 22
                    self.menu_font_size = int(self.font_size * 0.8)
                    self.small_font_size = int(self.font_size * 0.7)
                    self.large_font_size = int(self.font_size * 1.2)

                    # 保存默认设置到数据库
                    self.save_font_settings()
        except Exception as e:
            # 使用默认设置 - 固定为22号
            self.font_size = 22
            self.menu_font_size = int(self.font_size * 0.8)
            self.small_font_size = int(self.font_size * 0.7)
            self.large_font_size = int(self.font_size * 1.2)
            print(f"加载字体设置失败: {e}，使用默认设置: {self.font_size}")

    def save_font_settings(self):
        """保存字体设置到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 删除旧的字体设置
                conn.execute("DELETE FROM ui_settings WHERE key = 'font_size'")

                # 插入新的字体设置
                conn.execute(
                    "INSERT INTO ui_settings (key, value) VALUES (?, ?)",
                    ("font_size", str(self.font_size)),
                )
                conn.commit()
        except Exception as e:
            print(f"保存字体设置失败: {e}")

    def show_font_menu(self):
        """显示字体菜单"""
        x = self.btn_font.winfo_rootx()
        y = self.btn_font.winfo_rooty() + self.btn_font.winfo_height()
        self.font_menu.post(x, y)

    def show_zoom_menu(self):
        """显示缩放菜单"""
        x = self.btn_zoom.winfo_rootx()
        y = self.btn_zoom.winfo_rooty() + self.btn_zoom.winfo_height()
        self.zoom_menu.post(x, y)

    def zoom_reset_wrapper(self):
        """还原缩放包装函数"""
        self.reset_zoom()
        # 延迟重新显示菜单，确保当前操作完成
        self.root.after(50, self.show_zoom_menu)

    def zoom_in_wrapper(self):
        """放大包装函数"""
        self.zoom_in()
        # 延迟重新显示菜单，确保当前操作完成
        self.root.after(50, self.show_zoom_menu)

    def zoom_out_wrapper(self):
        """缩小包装函数"""
        self.zoom_out()
        # 延迟重新显示菜单，确保当前操作完成
        self.root.after(50, self.show_zoom_menu)

    def set_font_size(self, size):
        """设置特定字体大小"""
        self.font_size = size
        self.menu_font_size = int(self.font_size * 0.8)
        self.small_font_size = int(self.font_size * 0.7)
        self.large_font_size = int(self.font_size * 1.2)

        # 更新界面上的所有字体
        self.update_all_fonts()

        # 保存字体设置到数据库
        self.save_font_settings()

    def update_all_fonts(self):
        """更新界面上的所有字体"""
        try:
            # 更新菜单框架中的所有按钮字体
            for child in self.menu_frame.winfo_children():
                try:
                    if isinstance(child, tk.Button):
                        child.configure(font=(self.default_font, self.menu_font_size))
                    elif isinstance(
                        child, tk.Frame
                    ):  # 处理嵌套的帧，如关键帧控制按钮组
                        for sub_child in child.winfo_children():
                            try:
                                if isinstance(sub_child, tk.Button):
                                    sub_child.configure(
                                        font=(self.default_font, self.menu_font_size)
                                    )
                            except Exception:
                                pass
                    elif isinstance(child, ttk.Label):  # 更新标签字体
                        child.configure(font=(self.default_font, self.menu_font_size))
                except Exception:
                    pass

            # 更新搜索相关组件的字体
            self.apply_search_fonts()

            # 更新项目树字体
            self.apply_project_tree_fonts()

            # 更新菜单项字体
            if hasattr(self, "import_menu"):
                try:
                    for i in range(self.import_menu.index("end") + 1):
                        self.import_menu.entryconfig(
                            i, font=(self.default_font, self.menu_font_size)
                        )
                except Exception:
                    pass

            if hasattr(self, "font_menu"):
                try:
                    for i in range(self.font_menu.index("end") + 1):
                        self.font_menu.entryconfig(
                            i, font=(self.default_font, self.menu_font_size)
                        )
                except Exception:
                    pass

            if hasattr(self, "zoom_menu"):
                try:
                    for i in range(self.zoom_menu.index("end") + 1):
                        self.zoom_menu.entryconfig(
                            i, font=(self.default_font, self.menu_font_size)
                        )
                except Exception:
                    pass

            # 更新右键菜单字体
            if hasattr(self, "context_menu"):
                try:
                    for i in range(self.context_menu.index("end") + 1):
                        self.context_menu.entryconfig(
                            i, font=(self.default_font, self.menu_font_size)
                        )
                except Exception:
                    pass

            # 更新脚本窗口字体（如果存在）
            self._update_script_window_fonts()

            # 强制更新UI
            self.root.update_idletasks()
        except Exception as e:
            print(f"更新字体失败: {e}")

    def _update_script_window_fonts(self):
        """更新脚本窗口的字体（如果窗口存在）"""
        try:
            # 查找脚本窗口
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Toplevel) and widget.title() == "脚本信息编辑":
                    # 找到脚本窗口，更新其字体
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Frame):
                            for sub_child in child.winfo_children():
                                if isinstance(sub_child, tk.Label):
                                    if "关键帧时间脚本" in sub_child.cget("text"):
                                        # 标题
                                        sub_child.configure(
                                            font=(
                                                self.default_font,
                                                self.large_font_size,
                                                "bold",
                                            )
                                        )
                                    else:
                                        # 说明文字
                                        sub_child.configure(
                                            font=(
                                                self.default_font,
                                                self.menu_font_size,
                                            )
                                        )
                                elif isinstance(sub_child, tk.Button):
                                    # 保存按钮
                                    sub_child.configure(
                                        font=(self.default_font, self.menu_font_size)
                                    )
                                elif isinstance(sub_child, tk.Frame):
                                    # 文本框框架
                                    for text_child in sub_child.winfo_children():
                                        if isinstance(text_child, tk.Text):
                                            # 文本框
                                            text_child.configure(
                                                font=(self.default_font, self.font_size)
                                            )
                                            break
                    break
        except Exception as e:
            print(f"更新脚本窗口字体失败: {e}")

    def sync_all_folders(self):
        """同步所有文件夹内容（优化版本）"""
        try:
            # 禁用同步按钮，防止重复操作
            self.btn_sync.config(state="disabled", text="同步中...")
            self.root.update()

            # 使用优化的数据库管理器
            with self.db_manager.get_connection() as conn:
                # 获取所有文件夹
                cursor = conn.execute("SELECT id, path FROM folders")
                folders = cursor.fetchall()

                # 获取所有单独的图片
                cursor = conn.execute(
                    "SELECT id, path FROM images WHERE folder_id IS NULL"
                )
                standalone_images = cursor.fetchall()

                # 如果没有文件夹和单独图片，直接返回
                if not folders and not standalone_images:
                    self.btn_sync.config(state="normal", text="同步")
                    return

                # 取消同步标志
                cancel_sync = False

                # 同步统计
                stats = {
                    "added": 0,
                    "removed": 0,
                    "updated": 0,
                    "scanned_folders": 0,
                    "scanned_files": 0,
                }

                # 同步计数器
                current_count = 0

                # 使用多线程扫描文件夹
                def scan_folder(folder_info):
                    """扫描单个文件夹的文件"""
                    folder_id, folder_path = folder_info
                    try:
                        folder_path_obj = Path(folder_path)
                        if not folder_path_obj.exists():
                            return folder_id, [], []

                        # 获取文件夹中的所有图片文件
                        extensions = (".jpg", ".jpeg", ".png", ".bmp", ".gi", ".ti")
                        image_files = []

                        for file_path in folder_path_obj.rglob("*"):
                            if (
                                file_path.is_file()
                                and file_path.suffix.lower() in extensions
                            ):
                                image_files.append(str(file_path))

                        # 使用自定义排序函数对文件进行排序
                        image_files.sort(key=self.get_sort_key)

                        return folder_id, image_files, []
                    except Exception as e:
                        return folder_id, [], [str(e)]

                # 批量扫描文件夹
                from concurrent.futures import ThreadPoolExecutor, as_completed

                folder_scan_results = {}
                with ThreadPoolExecutor(max_workers=4) as executor:
                    future_to_folder = {
                        executor.submit(scan_folder, folder_info): folder_info
                        for folder_info in folders
                    }

                    for future in as_completed(future_to_folder):
                        if cancel_sync:
                            break

                        folder_info = future_to_folder[future]
                        folder_id, image_files, errors = future.result()
                        folder_scan_results[folder_id] = (image_files, errors)

                        # 更新进度
                        current_count += 1
                        stats["scanned_folders"] += 1
                        stats["scanned_files"] += len(image_files)

                        if errors:
                            print(f"扫描文件夹出错: {folder_info[1]}, 错误: {errors}")

                if cancel_sync:
                    self.btn_sync.config(state="normal", text="同步")
                    return

                # 批量处理数据库操作

                # 使用事务进行批量操作
                with self.db_manager.transaction() as trans_conn:
                    batch_operations = []

                    for folder_id, (image_files, errors) in folder_scan_results.items():
                        try:
                            # 检查是否为手动排序文件夹
                            cursor = trans_conn.execute(
                                """
                                SELECT is_manual_sort FROM manual_sort_folders
                                WHERE folder_id = ?
                            """,
                                (folder_id,),
                            )
                            manual_sort_result = cursor.fetchone()
                            is_manual_sort = (
                                manual_sort_result and manual_sort_result[0]
                            )

                            # 获取数据库中该文件夹的所有图片
                            cursor = trans_conn.execute(
                                "SELECT id, path FROM images WHERE folder_id = ?",
                                (folder_id,),
                            )
                            db_images = {row[1]: row[0] for row in cursor.fetchall()}

                            # 检查需要删除的图片
                            for db_path, img_id in db_images.items():
                                if not Path(db_path).exists():
                                    batch_operations.append(
                                        {
                                            "query": "DELETE FROM images WHERE id = ?",
                                            "params": (img_id,),
                                        }
                                    )
                                    stats["removed"] += 1

                            # 收集需要添加的新图片
                            new_images = []
                            for file_path in image_files:
                                if file_path not in db_images:
                                    name = Path(file_path).stem
                                    new_images.append((name, file_path))

                            # 如果有新图片需要添加
                            if new_images:
                                if is_manual_sort:
                                    # 手动排序文件夹：新图片添加到末尾，不改变现有顺序
                                    cursor = trans_conn.execute(
                                        """
                                        SELECT COALESCE(MAX(order_index), -1)
                                        FROM images
                                        WHERE folder_id = ?
                                    """,
                                        (folder_id,),
                                    )
                                    max_order = cursor.fetchone()[0]

                                    # 只添加新图片，不更新现有图片的order_index
                                    for i, (name, file_path) in enumerate(new_images):
                                        batch_operations.append(
                                            {
                                                "query": "INSERT INTO images (name, path, folder_id, order_index) VALUES (?, ?, ?, ?)",
                                                "params": (
                                                    name,
                                                    file_path,
                                                    folder_id,
                                                    max_order + 1 + i,
                                                ),
                                            }
                                        )
                                        stats["added"] += 1
                                else:
                                    # 自动排序文件夹：重新对文件夹中所有图片排序（包括新图片）
                                    all_paths = [
                                        path
                                        for path in db_images.keys()
                                        if Path(path).exists()
                                    ] + [img[1] for img in new_images]
                                    all_paths.sort(key=self.get_sort_key)

                                    # 批量更新order_index和添加新图片
                                    for i, path in enumerate(all_paths):
                                        if path in db_images:
                                            # 更新已有图片的order_index
                                            batch_operations.append(
                                                {
                                                    "query": "UPDATE images SET order_index = ? WHERE id = ?",
                                                    "params": (i, db_images[path]),
                                                }
                                            )
                                            stats["updated"] += 1
                                        else:
                                            # 添加新图片
                                            for name, file_path in new_images:
                                                if file_path == path:
                                                    batch_operations.append(
                                                        {
                                                            "query": "INSERT INTO images (name, path, folder_id, order_index) VALUES (?, ?, ?, ?)",
                                                            "params": (
                                                                name,
                                                                file_path,
                                                                folder_id,
                                                                i,
                                                            ),
                                                        }
                                                    )
                                                    stats["added"] += 1
                                                    break

                        except Exception as e:
                            print(f"处理文件夹数据失败: {folder_id}, 错误: {e}")

                    # 执行批量操作
                    if batch_operations:
                        for i, operation in enumerate(batch_operations):
                            if cancel_sync:
                                break
                            trans_conn.execute(operation["query"], operation["params"])

                            # 更新进度
                            pass

                if cancel_sync:
                    self.btn_sync.config(state="normal", text="同步")
                    return

                # 同步单独的图片

                standalone_operations = []
                for img_id, img_path in standalone_images:
                    try:
                        # 检查图片是否存在
                        if not Path(img_path).exists():
                            # 图片已被删除，从数据库中移除
                            standalone_operations.append(
                                {
                                    "query": "DELETE FROM images WHERE id = ?",
                                    "params": (img_id,),
                                }
                            )
                            stats["removed"] += 1
                    except Exception as e:
                        print(f"检查单独图片失败: {img_path}, 错误: {e}")

                # 执行单独图片的批量操作
                if standalone_operations:
                    with self.db_manager.transaction() as trans_conn:
                        for operation in standalone_operations:
                            trans_conn.execute(operation["query"], operation["params"])

                # 重新整理项目顺序
                with self.db_manager.get_connection() as conn:
                    self.reorganize_projects(conn)

                # 重新加载项目列表
                self.load_projects()

        except Exception as e:
            print(f"同步失败: {e}")
        finally:
            # 恢复同步按钮
            self.btn_sync.config(state="normal", text="同步")

    def quick_sync_check(self):
        """快速同步检查 - 仅检查是否有明显变化"""
        try:
            # 获取所有文件夹路径
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM folders")
                folder_count = cursor.fetchone()[0]

                cursor = conn.execute("SELECT COUNT(*) FROM images")
                image_count = cursor.fetchone()[0]

            # 如果数据库中没有任何数据，直接执行完整同步
            if folder_count == 0 and image_count == 0:
                self.sync_all_folders()
                return

            # 获取所有文件夹路径
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("SELECT path FROM folders")
                folder_paths = [row[0] for row in cursor.fetchall()]

            # 如果没有文件夹但有图片记录，可能是数据不一致，需要同步
            if not folder_paths and image_count > 0:
                self.sync_all_folders()
                return

            # 快速检查是否有文件夹不存在或明显的文件数量变化
            needs_sync = False
            for folder_path in folder_paths:
                folder_path_obj = Path(folder_path)
                if not folder_path_obj.exists():
                    needs_sync = True
                    break

                # 检查是否有子文件夹（递归扫描的情况）
                try:
                    extensions = (".jpg", ".jpeg", ".png", ".bmp", ".gi", ".ti")
                    # 使用递归扫描来匹配同步逻辑
                    current_files = sum(
                        1
                        for f in folder_path_obj.rglob("*")
                        if f.is_file() and f.suffix.lower() in extensions
                    )

                    # 获取数据库中的文件数量
                    with self.db_manager.get_connection() as conn:
                        cursor = conn.execute(
                            "SELECT COUNT(*) FROM images WHERE folder_id = (SELECT id FROM folders WHERE path = ?)",
                            (folder_path,),
                        )
                        db_count = cursor.fetchone()[0]

                    # 如果文件数量差异超过阈值，需要同步
                    if abs(current_files - db_count) > 0:
                        needs_sync = True
                        break

                except Exception:
                    # 如果检查出错，保守起见进行同步
                    needs_sync = True
                    break

            # 如果需要同步，自动执行
            if needs_sync:
                self.sync_all_folders()
            else:
                # 即使跳过同步，也要确保项目列表已加载
                self.load_projects()

        except Exception as e:
            print(f"快速同步检查失败: {e}")
            # 如果检查失败，执行完整同步
            self.sync_all_folders()

    def force_update_projection(self):
        """强制更新投影窗口"""
        if self.second_window and self.image:
            try:
                # 获取选定的屏幕
                selected = self.screen_combo.current()
                screen = self.screens[selected]
                screen_width = screen.width
                screen_height = screen.height

                # 统一使用与第一张图片相同的计算方式
                if self.original_mode:
                    # 原图模式下需要判断是否需要缩放以适应屏幕
                    width_ratio = screen_width / self.image.width
                    height_ratio = screen_height / self.image.height

                    # 选择较小的缩放比例以确保完整显示
                    scale_ratio = min(width_ratio, height_ratio)

                    # 始终应用缩放比例，即使图片小于屏幕
                    new_width = int(self.image.width * scale_ratio)
                    new_height = int(self.image.height * scale_ratio)
                else:
                    # 正常模式下的缩放逻辑
                    base_ratio = screen_width / self.image.width
                    final_ratio = base_ratio * self.zoom_ratio
                    new_width = int(self.image.width * final_ratio)
                    new_height = int(self.image.height * final_ratio)

                # 统一的实时处理逻辑
                resized_image = self.image.resize(
                    (new_width, new_height), Image.Resampling.LANCZOS
                )

                # 根据模式应用效果
                if self.is_inverted:
                    resized_image = self.apply_yellow_text_effect(resized_image)
                elif self.is_simple_inverted:
                    resized_image = self.simple_invert_image(resized_image)
                self.second_photo = ImageTk.PhotoImage(resized_image)

                # 将图片放在顶部，水平居中
                x = max(0, (screen_width - new_width) // 2)

                # 如果已有图片，删除旧的
                if hasattr(self, "second_image_on_canvas"):
                    self.second_canvas.delete(self.second_image_on_canvas)

                # 显示新图片 - 使用与show_on_second_screen相同的锚点和位置
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.second_photo
                )

                # 设置投影滚动区域 - 允许将底部内容拉到顶部显示
                if new_height > screen_height:
                    scroll_height = (
                        new_height + screen_height
                    )  # 图片高度 + 一个屏幕高度的额外空间
                else:
                    scroll_height = screen_height

                self.second_canvas.configure(
                    scrollregion=(0, 0, screen_width, scroll_height)
                )

                # 立即更新画布
                self.second_canvas.update_idletasks()

                print("投影窗口已强制更新")
            except Exception as e:
                print(f"强制更新投影失败: {e}")

    def load_image(self, path):
        """优化的图片加载方法"""
        try:
            # 处理虚拟路径：如果路径包含根目录标识，提取真实路径
            real_path = path
            if "#root_" in path:
                real_path = path.split("#root_")[0]

            # 清除当前图片
            self.clear_current_image()

            # 使用draft模式优化JPEG加载
            try:
                with Image.open(real_path) as temp_img:
                    # 计算合适的draft尺寸（预估显示尺寸的1.5倍以保证质量）
                    canvas_width, canvas_height = self.get_effective_screen_size(
                        is_projection_screen=False
                    )
                    draft_size = (int(canvas_width * 1.5), int(canvas_height * 1.5))

                    # 对于JPEG文件使用draft模式优化加载
                    if real_path.lower().endswith((".jpg", ".jpeg")):
                        temp_img.draft("RGB", draft_size)

                    # 加载图片
                    self.image = temp_img.copy()
            except Exception:
                # 如果draft失败，使用常规方式加载
                self.image = Image.open(real_path)

            # 记录图片路径（保存原始路径，包括虚拟路径标识）
            self.current_path = path

            # 移除预生成缓存，改为完全按需处理

            # 获取图片ID
            result = self.safe_db_execute(
                "SELECT id FROM images WHERE path = ?", (path,), fetch="one"
            )
            if result:
                self.current_image_id = result[0]

                # 在原图模式下，查找相似图片
                if self.original_mode:
                    self.yuantu_manager.find_similar_images(self.current_image_id)

            # 更新图片显示
            self.update_image()

            # 重置滚动条到顶部
            self.canvas.yview_moveto(0)

            # 重置关键帧索引
            self.current_keyframe_index = -1

            # 更新关键帧指示器
            self.update_keyframe_indicators()

            # 更新预览线
            self.update_preview_lines()

            # 如果投影窗口已打开，更新投影
            if self.second_window and self.sync_enabled:
                self.force_update_projection()
                # 同时重置投影窗口的滚动条
                self.second_canvas.yview_moveto(0)

            # 延迟更新按钮状态，确保图片加载完成后按钮状态正确
            self.root.after(100, self.update_button_status)

            return True
        except Exception as e:
            print(f"加载图片失败: {e}")
            return False

    def apply_yellow_text_effect(self, image):
        """应用黄字效果（黑底黄字，底色强制纯黑）- 性能优化版本"""
        try:
            # 性能优化：检查图像是否已经是黄字效果
            if hasattr(image, "_is_yellow_processed") and image._is_yellow_processed:
                return image

            # 转换为RGBA模式，便于处理透明度
            if image.mode != "RGBA":
                image = image.convert("RGBA")
            img_array = np.array(image)

            # 性能优化：使用更快的亮度计算方法
            # 使用权重平均而不是简单平均，提高处理速度
            luminance = (
                0.299 * img_array[..., 0]
                + 0.587 * img_array[..., 1]
                + 0.114 * img_array[..., 2]
            )
            is_text = luminance < 150  # 阈值可调整

            # 性能优化：直接操作数组，减少内存分配
            result = np.zeros_like(img_array)
            result[..., 3] = 255  # 设置alpha通道

            # 使用配置的RGB颜色
            result[is_text, 0] = self.yellow_text_rgb["r"]  # R
            result[is_text, 1] = self.yellow_text_rgb["g"]  # G
            result[is_text, 2] = self.yellow_text_rgb["b"]  # B
            result[is_text, 3] = img_array[is_text, 3]  # 保留原透明度

            # 保留原图透明区域
            transparent_mask = img_array[..., 3] == 0
            result[transparent_mask, 3] = 0

            final_image = Image.fromarray(result)
            # 标记图像已处理，避免重复处理
            final_image._is_yellow_processed = True
            return final_image
        except Exception as e:
            print(f"应用黄字效果失败: {e}")
            return image

    def create_canvas_context_menu(self):
        """创建画布右键菜单"""
        self.canvas_menu = tk.Menu(
            self.root, tearoff=0, font=(self.default_font, int(self.font_size * 0.8))
        )

        # 添加滚动速度子菜单
        self.scroll_speed_menu = tk.Menu(
            self.canvas_menu,
            tearoff=0,
            font=(self.default_font, int(self.font_size * 0.8)),
        )
        self.scroll_speed_var = tk.StringVar()
        self.scroll_speed_var.set(str(self.scroll_duration))
        for speed in [
            "0",
            "0.5",
            "1.0",
            "2.0",
            "3.0",
            "4.0",
            "5.0",
            "6.0",
            "7.0",
            "8.0",
            "9.0",
            "10.0",
        ]:
            self.scroll_speed_menu.add_radiobutton(
                label=f"{speed}秒",
                variable=self.scroll_speed_var,
                value=speed,
                command=self.set_scroll_speed,
                font=(self.default_font, int(self.font_size * 0.8)),
            )
        self.canvas_menu.add_cascade(
            label="滚动速度",
            menu=self.scroll_speed_menu,
            font=(self.default_font, int(self.font_size * 0.8)),
        )

        # 添加黄字颜色子菜单
        self.yellow_color_menu = tk.Menu(
            self.canvas_menu,
            tearoff=0,
            font=(self.default_font, int(self.font_size * 0.8)),
        )
        self.yellow_color_var = tk.StringVar()
        # 设置当前选中的颜色名称
        self.yellow_color_var.set(self.current_yellow_color_name)
        self.update_yellow_color_menu()  # 用新的方法动态刷新
        self.canvas_menu.add_cascade(
            label="黄字颜色",
            menu=self.yellow_color_menu,
            font=(self.default_font, int(self.font_size * 0.8)),
        )

    def show_canvas_context_menu(self, event):
        """显示画布右键菜单"""
        # 确保有图片加载时才显示菜单
        if self.image:
            self.canvas_menu.post(event.x_root, event.y_root)

    def set_scroll_speed(self):
        """设置滚动速度"""
        try:
            # 获取选择的速度值
            speed_str = self.scroll_speed_var.get()
            speed = float(speed_str)

            # 更新滚动时间
            self.scroll_duration = speed

            # 保存设置到数据库
            self.save_scroll_speed_settings()

        except Exception as e:
            print(f"设置滚动速度失败: {e}")

    def load_scroll_speed_settings(self):
        """从数据库加载滚动速度设置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT value FROM ui_settings WHERE key = 'scroll_speed'"
                )
                result = cursor.fetchone()

                if result:
                    self.scroll_duration = float(result[0])
                    self.scroll_speed_var.set(str(self.scroll_duration))
                    # print(f"已加载滚动速度设置: {self.scroll_duration}秒")
                else:
                    # 使用默认设置 - 改为0.0秒
                    self.scroll_duration = 0.0
                    self.scroll_speed_var.set("0.0")
                    # print(f"使用默认滚动速度设置: {self.scroll_duration}秒")

                    # 保存默认设置到数据库
                    self.save_scroll_speed_settings()
        except Exception as e:
            # 使用默认设置 - 改为0.0秒
            self.scroll_duration = 0.0
            self.scroll_speed_var.set("0.0")
            print(f"加载滚动速度设置失败: {e}，使用默认设置: {self.scroll_duration}秒")

    def save_scroll_speed_settings(self):
        """保存滚动速度设置到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 删除旧的设置
                conn.execute("DELETE FROM ui_settings WHERE key = 'scroll_speed'")

                # 插入新的设置
                conn.execute(
                    "INSERT INTO ui_settings (key, value) VALUES (?, ?)",
                    ("scroll_speed", str(self.scroll_duration)),
                )
                conn.commit()
                # print(f"已保存滚动速度设置: {self.scroll_duration}秒")
        except Exception as e:
            print(f"保存滚动速度设置失败: {e}")

    def on_tree_press(self, event):
        """处理树形控件的鼠标按下事件（用于拖放操作）"""
        # 获取点击的项目
        item = self.project_tree.identify_row(event.y)
        if item:
            # 记录拖动开始的项目和位置
            self.drag_data["item"] = item
            self.drag_data["x"] = event.x
            self.drag_data["y"] = event.y

    def on_tree_motion(self, event):
        """处理树形控件的鼠标移动事件（用于拖放操作）"""
        # 检查是否有正在拖动的项目
        if self.drag_data["item"]:
            # 获取当前鼠标位置的项目
            current_target = self.project_tree.identify_row(event.y)

            # 清除之前的高亮
            if (
                self.drag_data.get("target")
                and self.drag_data["target"] != current_target
            ):
                try:
                    # 恢复之前目标的正常样式
                    self.project_tree.set(
                        self.drag_data["target"],
                        "name",
                        self.project_tree.set(self.drag_data["target"], "name").replace(
                            "→ ", ""
                        ),
                    )
                except Exception:
                    pass

            # 设置新的目标高亮
            if current_target and current_target != self.drag_data["item"]:
                source_parent = self.project_tree.parent(self.drag_data["item"])
                target_parent = self.project_tree.parent(current_target)

                # 只有在同一父级下才显示高亮
                if source_parent == target_parent:
                    try:
                        current_text = self.project_tree.set(current_target, "name")
                        if not current_text.startswith("→ "):
                            self.project_tree.set(
                                current_target, "name", "→ " + current_text
                            )
                        self.drag_data["target"] = current_target
                    except Exception:
                        pass
                else:
                    self.drag_data["target"] = None
            else:
                self.drag_data["target"] = None

    def on_tree_release(self, event):
        """处理树形控件的鼠标释放事件（用于拖放操作）"""
        # 检查是否有正在拖动的项目
        if self.drag_data["item"]:
            # 获取释放位置的项目
            target = self.project_tree.identify_row(event.y)

            # 如果有目标项目且不是同一个项目
            if target and target != self.drag_data["item"]:
                try:
                    # 获取源项目和目标项目的ID
                    source_id = self.drag_data["item"]
                    target_id = target

                    # 检查是否是文件夹项目
                    is_source_folder = source_id.startswith("folder_")
                    is_target_folder = target_id.startswith("folder_")

                    # 如果源是图片且目标是文件夹，则将图片移动到文件夹中
                    if not is_source_folder and is_target_folder:
                        # 从文件夹ID中提取数字部分
                        folder_id = int(target_id.split("_")[1])
                        image_id = int(source_id)

                        # 更新数据库
                        with sqlite3.connect(self.db_path) as conn:
                            conn.execute(
                                """
                                UPDATE images
                                SET folder_id = ?
                                WHERE id = ?
                            """,
                                (folder_id, image_id),
                            )
                            conn.commit()

                        # 重新加载项目列表
                        self.load_projects()

                    # 如果源和目标都是图片，则调整顺序
                    elif not is_source_folder and not is_target_folder:
                        # 获取源和目标的父项目
                        source_parent = self.project_tree.parent(source_id)
                        target_parent = self.project_tree.parent(target_id)

                        # 如果在同一个父项目下，则调整顺序
                        if source_parent == target_parent:
                            # 执行手动排序
                            self.manual_reorder_images(
                                source_id, target_id, source_parent
                            )

                except Exception as e:
                    print(f"拖放操作失败: {e}")

            # 清除目标高亮
            if self.drag_data.get("target"):
                try:
                    current_text = self.project_tree.set(
                        self.drag_data["target"], "name"
                    )
                    if current_text.startswith("→ "):
                        self.project_tree.set(
                            self.drag_data["target"],
                            "name",
                            current_text.replace("→ ", ""),
                        )
                except Exception:
                    pass

            # 重置拖放数据
            self.drag_data = {"item": None, "x": 0, "y": 0, "target": None}

    def manual_reorder_images(self, source_id, target_id, parent_folder):
        """手动重新排序图片"""
        try:
            # 记住当前选中的项目和展开状态
            current_selection = self.project_tree.selection()
            expanded_items = []
            for item in self.project_tree.get_children():
                if self.project_tree.item(item, "open"):
                    expanded_items.append(item)

            with sqlite3.connect(self.db_path) as conn:
                if parent_folder:
                    # 文件夹内的图片
                    folder_id = int(parent_folder.split("_")[1])

                    # 标记该文件夹为手动排序
                    conn.execute(
                        """
                        INSERT OR REPLACE INTO manual_sort_folders
                        (folder_id, is_manual_sort, last_manual_sort_time)
                        VALUES (?, 1, CURRENT_TIMESTAMP)
                    """,
                        (folder_id,),
                    )

                    # 获取文件夹内所有图片的当前顺序
                    cursor = conn.execute(
                        """
                        SELECT id, order_index
                        FROM images
                        WHERE folder_id = ?
                        ORDER BY order_index
                    """,
                        (folder_id,),
                    )
                    images = cursor.fetchall()

                    # 重新计算顺序
                    new_order = self._calculate_new_order(
                        images, int(source_id), int(target_id)
                    )

                    # 批量更新顺序
                    for img_id, new_index in new_order.items():
                        conn.execute(
                            """
                            UPDATE images
                            SET order_index = ?
                            WHERE id = ? AND folder_id = ?
                        """,
                            (new_index, img_id, folder_id),
                        )
                else:
                    # 独立图片
                    cursor = conn.execute(
                        """
                        SELECT id, order_index
                        FROM images
                        WHERE folder_id IS NULL
                        ORDER BY order_index
                    """
                    )
                    images = cursor.fetchall()

                    # 重新计算顺序
                    new_order = self._calculate_new_order(
                        images, int(source_id), int(target_id)
                    )

                    # 批量更新顺序
                    for img_id, new_index in new_order.items():
                        conn.execute(
                            """
                            UPDATE images
                            SET order_index = ?
                            WHERE id = ? AND folder_id IS NULL
                        """,
                            (new_index, img_id),
                        )

                conn.commit()

            # 重新加载项目列表
            self.load_projects()

            # 恢复展开状态
            for item in expanded_items:
                try:
                    self.project_tree.item(item, open=True)
                except Exception:
                    pass

            # 恢复选中状态，优先选中被移动的图片
            try:
                self.project_tree.selection_set(source_id)
                self.project_tree.focus(source_id)
                # 确保选中的项目可见
                self.project_tree.see(source_id)
            except Exception:
                # 如果无法选中源项目，尝试恢复之前的选中状态
                if current_selection:
                    try:
                        self.project_tree.selection_set(current_selection[0])
                        self.project_tree.focus(current_selection[0])
                    except Exception:
                        pass

        except Exception as e:
            print(f"手动排序失败: {e}")

    def _calculate_new_order(self, images, source_id, target_id):
        """计算新的排序顺序"""
        # 创建ID到索引的映射
        id_to_index = {img_id: idx for idx, (img_id, _) in enumerate(images)}

        # 获取源和目标的当前索引
        source_index = id_to_index[source_id]
        target_index = id_to_index[target_id]

        # 创建新的顺序列表
        image_ids = [img_id for img_id, _ in images]

        # 移除源项目
        image_ids.pop(source_index)

        # 插入到目标位置
        if source_index < target_index:
            # 向下拖拽，插入到目标位置
            image_ids.insert(target_index - 1, source_id)
        else:
            # 向上拖拽，插入到目标位置
            image_ids.insert(target_index, source_id)

        # 返回新的顺序映射
        return {img_id: idx for idx, img_id in enumerate(image_ids)}

    def reset_folder_sort(self):
        """重置文件夹排序为自动排序"""
        try:
            # 获取当前选中的项目
            selected_items = self.project_tree.selection()
            if not selected_items:
                return

            selected_item = selected_items[0]
            if not selected_item.startswith("folder_"):
                return

            folder_id = int(selected_item.split("_")[1])

            # 确认对话框
            from tkinter import messagebox

            result = messagebox.askyesno(
                "确认重置",
                "确定要重置此文件夹的排序吗？\n将按照文件名自动排序。",
                parent=self.root,
            )

            if not result:
                return

            with sqlite3.connect(self.db_path) as conn:
                # 移除手动排序标记
                conn.execute(
                    """
                    DELETE FROM manual_sort_folders
                    WHERE folder_id = ?
                """,
                    (folder_id,),
                )

                # 获取文件夹内所有图片
                cursor = conn.execute(
                    """
                    SELECT id, path
                    FROM images
                    WHERE folder_id = ?
                """,
                    (folder_id,),
                )
                images = cursor.fetchall()

                # 按文件名重新排序
                sorted_images = sorted(images, key=lambda x: self.get_sort_key(x[1]))

                # 更新排序索引
                for index, (img_id, _) in enumerate(sorted_images):
                    conn.execute(
                        """
                        UPDATE images
                        SET order_index = ?
                        WHERE id = ?
                    """,
                        (index, img_id),
                    )

                conn.commit()

            # 重新加载项目列表
            self.load_projects()

            messagebox.showinfo("完成", "文件夹排序已重置", parent=self.root)

        except Exception as e:
            print(f"重置排序失败: {e}")
            messagebox.showerror("错误", f"重置排序失败: {e}", parent=self.root)

    def setup_global_hotkeys(self):
        """设置全局热键"""
        if not KEYBOARD_AVAILABLE:
            print("keyboard库未安装，跳过全局热键设置")
            return

        # 避免重复设置
        if self.global_hotkeys_enabled:
            return

        try:
            # 先清理可能存在的旧热键
            try:
                keyboard.remove_hotkey("page up")
                keyboard.remove_hotkey("page down")
                keyboard.remove_hotkey("escape")
            except Exception:
                pass  # 忽略清理错误

            # 使用keyboard库注册全局热键，只在按键按下时触发
            def on_pageup_press():
                # 设置焦点到画布，确保不影响项目列表
                self.root.after(
                    0, lambda: (self.canvas.focus_set(), self.handle_pageup_key())
                )

            def on_pagedown_press():
                # 设置焦点到画布，确保不影响项目列表
                self.root.after(
                    0, lambda: (self.canvas.focus_set(), self.handle_pagedown_key())
                )

            def on_escape_press():
                # ESC键结束投影
                self.root.after(0, lambda: self.handle_escape_key())

            # 删除播放、录制、次数相关的按键绑定
            pass

            # 使用add_hotkey替代on_press_key，更稳定
            keyboard.add_hotkey("page up", on_pageup_press)
            keyboard.add_hotkey("page down", on_pagedown_press)
            keyboard.add_hotkey("escape", on_escape_press)
            # 删除播放、录制、次数相关的按键绑定

            print("全局热键注册成功: PageUp, PageDown, Escape")

        except Exception as e:
            print(f"设置全局热键失败: {e}")

    def cleanup_global_hotkeys(self):
        """清理全局热键"""
        if not KEYBOARD_AVAILABLE:
            return

        # 只在已启用时才清理
        if not self.global_hotkeys_enabled:
            return

        try:
            # 改用更安全的清理方式，只清理特定的热键而不是全部
            keyboard.remove_hotkey("page up")
            keyboard.remove_hotkey("page down")
            keyboard.remove_hotkey("escape")
            # 删除播放、录制、次数相关的按键清理
            print("全局热键已清理")
        except Exception as e:
            # 如果单独移除失败，则尝试全部清理
            try:
                keyboard.unhook_all()
                print("全局热键已全部清理")
            except Exception:
                print(f"清理全局热键失败: {e}")

    def __del__(self):
        """析构函数，清理资源"""
        try:
            if hasattr(self, "global_hotkeys_enabled") and self.global_hotkeys_enabled:
                self.cleanup_global_hotkeys()
        except Exception:
            pass

    def manage_original_mark(
        self, item_type, item_id, action="check", mark_type="loop"
    ):
        """统一管理原图标记的添加、移除和检查 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.manage_original_mark(
            item_type, item_id, action, mark_type
        )

    def add_original_mark(self, item_type, item_id, mark_type="loop"):
        """添加原图标记 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.add_original_mark(item_type, item_id, mark_type)

    def remove_original_mark(self, item_type, item_id):
        """移除原图标记 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.remove_original_mark(item_type, item_id)

    def check_original_mark(self, item_type, item_id):
        """检查是否有原图标记 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.check_original_mark(item_type, item_id)

    def get_original_mark_type(self, item_type, item_id):
        """获取原图标记类型 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.get_original_mark_type(item_type, item_id)

    def should_use_original_mode(self, image_id):
        """判断图片是否应该使用原图模式 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.should_use_original_mode(image_id)

    def set_yellow_text_color(self, name):
        """切换黄字颜色并刷新图片"""
        if name in self.yellow_text_presets:
            self.current_yellow_color_name = name
            self.yellow_text_rgb = dict(self.yellow_text_presets[name])
            # 移除黄字缓存清理（已改为实时处理）
            if hasattr(self, "image_cache"):
                self.image_cache.clear()  # 清空所有缩放缓存
            # 移除黄字图片缓存重新生成（已改为实时处理）
            self.update_image()
            if self.second_window and self.sync_enabled:
                self.update_projection()
            self.save_config()

    def update_yellow_color_menu(self):
        """动态刷新黄字颜色菜单，包括自定义颜色和自定义入口"""
        self.yellow_color_menu.delete(0, "end")
        for name, rgb in self.yellow_text_presets.items():
            self.yellow_color_menu.add_radiobutton(
                label=name,
                variable=self.yellow_color_var,
                value=name,
                command=lambda n=name: self.set_yellow_text_color(n),
                font=(self.default_font, int(self.font_size * 0.8)),
            )
        self.yellow_color_menu.add_separator()
        self.yellow_color_menu.add_command(
            label="自定义...",
            command=self.add_custom_yellow_color,
            font=(self.default_font, int(self.font_size * 0.8)),
        )

    def add_custom_yellow_color(self):
        """弹窗输入自定义黄字颜色名称和RGB"""
        from tkinter import messagebox, simpledialog

        name = simpledialog.askstring(
            "自定义黄字颜色", "请输入颜色名称：", parent=self.root
        )
        if not name:
            return
        try:
            r = simpledialog.askinteger(
                "自定义黄字颜色",
                "请输入R值 (0-255)：",
                minvalue=0,
                maxvalue=255,
                parent=self.root,
            )
            if r is None:
                return
            g = simpledialog.askinteger(
                "自定义黄字颜色",
                "请输入G值 (0-255)：",
                minvalue=0,
                maxvalue=255,
                parent=self.root,
            )
            if g is None:
                return
            b = simpledialog.askinteger(
                "自定义黄字颜色",
                "请输入B值 (0-255)：",
                minvalue=0,
                maxvalue=255,
                parent=self.root,
            )
            if b is None:
                return
        except Exception:
            messagebox.showerror("输入错误", "RGB值必须是0-255之间的整数！")
            return
        # 检查名称是否重复
        if name in self.yellow_text_presets:
            messagebox.showerror("名称重复", "该名称已存在，请换一个名称！")
            return
        # 添加到预设
        self.yellow_text_presets[name] = {"r": r, "g": g, "b": b}
        # 保存到配置
        self.save_custom_yellow_color(name, r, g, b)
        # 刷新菜单
        self.update_yellow_color_menu()
        # 立即切换到新颜色
        self.set_yellow_text_color(name)

    def save_custom_yellow_color(self, name, r, g, b):
        """保存自定义颜色到config.json"""
        import json

        # 先读取原有配置
        config = {}
        if self.config_file.exists():
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
            except Exception:
                config = {}
        # 追加到custom_yellow_colors
        custom_list = config.get("custom_yellow_colors", [])
        custom_list.append({"name": name, "r": r, "g": g, "b": b})
        config["custom_yellow_colors"] = custom_list
        # 其他配置项保留
        config["zoom_ratio"] = getattr(self, "zoom_ratio", 1.0)
        config["zoom_step"] = getattr(self, "zoom_step", 1.1)
        config["yellow_color_name"] = getattr(self, "current_yellow_color_name", None)
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存自定义颜色失败: {e}")


class ImageCache:
    def __init__(self):
        self.memory_cache = LRUCache(150)  # 普通图片缓存
        self.preview_cache = LRUCache(50)  # 预览图缓存

    def clear(self):
        """清空所有缓存"""
        self.memory_cache.clear()
        self.preview_cache.clear()

    def get_image(self, path, size):
        """从内存缓存获取图片"""
        key = f"{path}_{size}"
        if key in self.memory_cache:
            return self.memory_cache[key]

        # 加载并缓存
        image = self.load_and_resize(path, size)
        self.memory_cache[key] = image
        return image

    def __getitem__(self, key):
        return self.memory_cache[key]

    def __setitem__(self, key, value):
        self.memory_cache[key] = value

    def __contains__(self, key):
        return key in self.memory_cache

    def __len__(self):
        """返回缓存中的项目数量"""
        return len(self.memory_cache)


if __name__ == "__main__":
    app = None
    try:
        root = tk.Tk()
        root.state("zoomed")
        app = ImageProjector(root)

        def on_closing():
            """程序关闭时的清理函数"""
            try:
                if (
                    app
                    and hasattr(app, "global_hotkeys_enabled")
                    and app.global_hotkeys_enabled
                ):
                    app.cleanup_global_hotkeys()
                    print("程序关闭时清理全局热键")

                # 关闭数据库管理器
                if app and hasattr(app, "db_manager"):
                    app.db_manager.close()
                    # print("数据库连接已关闭")

                # print("程序正在关闭...")
                root.quit()
                root.destroy()
            except Exception as e:
                print(f"关闭程序时出错: {e}")

        root.bind("<Configure>", lambda e: app.update_image())
        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
    except Exception as e:
        print(f"程序错误: {e}")
    finally:
        # 确保清理全局热键
        try:
            if (
                app
                and hasattr(app, "global_hotkeys_enabled")
                and app.global_hotkeys_enabled
            ):
                app.cleanup_global_hotkeys()
        except Exception:
            pass
