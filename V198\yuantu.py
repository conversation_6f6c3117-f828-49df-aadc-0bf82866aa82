import re
import sqlite3
import time
import tkinter as tk
from pathlib import Path
from tkinter import messagebox, ttk


class YuanTuManager:
    """原图模式管理器"""

    def __init__(self, main_app):
        """初始化原图管理器

        Args:
            main_app: 主程序实例
        """
        self.main_app = main_app
        self.db_path = main_app.db_path

        # 相似图片切换功能的状态变量
        self.similar_images = []
        self.current_similar_index = 0

    def toggle_original_mode(self):
        """切换原图模式"""
        self.main_app.original_mode = not self.main_app.original_mode

        # 更新按钮状态
        if self.main_app.original_mode:
            self.main_app.btn_original.configure(bg="#90EE90")  # 浅绿色

            # 在原图模式下，查找相似图片
            if hasattr(self.main_app, "current_image_id"):
                self.find_similar_images(self.main_app.current_image_id)
        else:
            self.main_app.btn_original.configure(bg="#f0f0f0")  # 恢复默认颜色

        # 更新图片显示
        self.main_app.update_image()

        # 确保投影窗口也更新
        if self.main_app.second_window and self.main_app.sync_enabled:
            self.main_app.force_update_projection()

    def mark_as_original(self, mark_type="loop"):
        """标记选中项目为原图"""
        selected = self.main_app.project_tree.selection()
        if not selected:
            return

        item_id = selected[0]
        is_folder = item_id.startswith("folder_")

        try:
            if is_folder:
                folder_id = int(item_id.split("_")[1])
                success = self.add_original_mark("folder", folder_id, mark_type)
                if success:
                    # 更新项目树显示，添加标记图标
                    self.update_project_tree_marks()
                    mark_text = (
                        "循环原图标记" if mark_type == "loop" else "顺序原图标记"
                    )
                    # print(f"文件夹标记成功: {mark_text}")
            else:
                # 检查是否在文件夹内
                parent_item = self.main_app.project_tree.parent(item_id)
                if parent_item:  # 在文件夹内的图片不允许单独标记
                    messagebox.showinfo(
                        "提示", "文件夹内的图片不能单独标记，请对文件夹进行标记"
                    )
                    return

                image_id = int(item_id)
                success = self.add_original_mark("image", image_id, mark_type)
                if success:
                    # 更新项目树显示，添加标记图标
                    self.update_project_tree_marks()

                    mark_text = (
                        "循环原图标记" if mark_type == "loop" else "顺序原图标记"
                    )
                    print(f"图片标记成功: {mark_text}")

                    # 如果当前显示的就是这张图片，立即应用原图模式
                    if (
                        hasattr(self.main_app, "current_image_id")
                        and self.main_app.current_image_id == image_id
                    ):
                        if not self.main_app.original_mode:
                            self.toggle_original_mode()

            if not success:
                messagebox.showerror("错误", "标记失败")

        except Exception as e:
            print(f"标记原图失败: {e}")
            messagebox.showerror("错误", f"标记失败: {e}")

    def unmark_original(self):
        """取消选中项目的原图标记"""
        selected = self.main_app.project_tree.selection()
        if not selected:
            return

        item_id = selected[0]
        is_folder = item_id.startswith("folder_")

        try:
            if is_folder:
                folder_id = int(item_id.split("_")[1])
                success = self.remove_original_mark("folder", folder_id)
                if success:
                    # 更新项目树显示，移除标记图标
                    self.update_project_tree_marks()
            else:
                # 检查是否在文件夹内
                parent_item = self.main_app.project_tree.parent(item_id)
                if parent_item:  # 在文件夹内的图片不允许单独取消标记
                    messagebox.showinfo(
                        "提示", "文件夹内的图片不能单独取消标记，请对文件夹进行操作"
                    )
                    return

                image_id = int(item_id)
                success = self.remove_original_mark("image", image_id)
                if success:
                    # 更新项目树显示，移除标记图标
                    self.update_project_tree_marks()

            if not success:
                messagebox.showerror("错误", "取消标记失败")

        except Exception as e:
            print(f"取消原图标记失败: {e}")
            messagebox.showerror("错误", f"取消标记失败: {e}")

    def update_project_tree_marks(self):
        """更新项目树中的原图标记显示"""
        try:
            # 遍历项目树中的所有项目，更新标记显示
            for item_id in self.main_app.project_tree.get_children():
                self._update_item_mark_display(item_id)

                # 如果是文件夹，也更新其子项目
                for child_id in self.main_app.project_tree.get_children(item_id):
                    self._update_item_mark_display(child_id)

        except Exception as e:
            print(f"更新项目树标记显示失败: {e}")

    def _update_item_mark_display(self, item_id):
        """更新单个项目的标记显示"""
        try:
            is_folder = item_id.startswith("folder_")
            current_values = self.main_app.project_tree.item(item_id, "values")
            if not current_values:
                return

            current_name = current_values[0]

            if is_folder:
                folder_id = int(item_id.split("_")[1])
                has_mark = self.check_original_mark("folder", folder_id)

                # 检查是否为手动排序文件夹
                is_manual_sort = False
                try:
                    with sqlite3.connect(self.main_app.db_path) as conn:
                        cursor = conn.execute(
                            """
                            SELECT is_manual_sort FROM manual_sort_folders
                            WHERE folder_id = ?
                        """,
                            (folder_id,),
                        )
                        result = cursor.fetchone()
                        is_manual_sort = result and result[0]
                except Exception as e:
                    print(f"检查手动排序状态失败: {e}")

                # 移除现有的标记前缀（包括手动排序标记）
                # 使用更安全的方式移除前缀
                clean_name = current_name
                prefixes_to_remove = [
                    "★ 🔢 ",
                    "☆ 🔢 ",
                    "★ ",
                    "☆ ",
                    "⭐ 🔢 ",
                    "⭐ ",
                    "[●] 🔢 ",
                    "[ ] 🔢 ",
                    "[●] ",
                    "[ ] ",
                ]

                for prefix in prefixes_to_remove:
                    if clean_name.startswith(prefix):
                        clean_name = clean_name[len(prefix) :]
                        break

                # 根据标记和排序类型确定图标
                if has_mark:
                    if is_manual_sort:
                        mark_icon = "★ 🔢 "  # 有标记且手动排序
                    else:
                        mark_icon = "★ "  # 有标记但自动排序
                else:
                    if is_manual_sort:
                        mark_icon = "☆ 🔢 "  # 无标记但手动排序
                    else:
                        mark_icon = "☆ "  # 无标记且自动排序

                new_name = mark_icon + clean_name
                self.main_app.project_tree.item(item_id, values=(new_name,))
            else:
                # 检查这个图片是否在文件夹内
                parent_item = self.main_app.project_tree.parent(item_id)
                if parent_item:  # 如果有父项目，说明在文件夹内，不显示标记
                    return

                # 只有独立图片才显示标记
                image_id = int(item_id)
                has_mark = self.check_original_mark("image", image_id)

                # 移除现有的标记前缀
                if current_name.startswith("● "):
                    clean_name = current_name[2:]
                else:
                    clean_name = current_name

                # 只有标记时才添加前缀
                if has_mark:
                    new_name = "● " + clean_name
                else:
                    new_name = clean_name
                self.main_app.project_tree.item(item_id, values=(new_name,))

        except Exception as e:
            print(f"更新项目标记显示失败: {e}")

    def manage_original_mark(
        self, item_type, item_id, action="check", mark_type="loop"
    ):
        """统一管理原图标记的添加、移除和检查"""
        if action == "add":
            result = self.main_app.safe_db_execute(
                "INSERT OR REPLACE INTO original_marks (item_type, item_id, mark_type) VALUES (?, ?, ?)",
                (item_type, item_id, mark_type),
                commit=True,
            )
            return result is not None
        elif action == "remove":
            result = self.main_app.safe_db_execute(
                "DELETE FROM original_marks WHERE item_type = ? AND item_id = ?",
                (item_type, item_id),
                commit=True,
            )
            return result is not None
        elif action == "get_type":
            result = self.main_app.safe_db_execute(
                "SELECT mark_type FROM original_marks WHERE item_type = ? AND item_id = ?",
                (item_type, item_id),
                fetch="one",
            )
            return result[0] if result else None
        else:  # check
            result = self.main_app.safe_db_execute(
                "SELECT COUNT(*) FROM original_marks WHERE item_type = ? AND item_id = ?",
                (item_type, item_id),
                fetch="one",
            )
            return result and result[0] > 0 if result else False

    def add_original_mark(self, item_type, item_id, mark_type="loop"):
        """添加原图标记"""
        return self.manage_original_mark(item_type, item_id, "add", mark_type)

    def remove_original_mark(self, item_type, item_id):
        """移除原图标记"""
        return self.manage_original_mark(item_type, item_id, "remove")

    def check_original_mark(self, item_type, item_id):
        """检查是否有原图标记"""
        return self.manage_original_mark(item_type, item_id, "check")

    def get_original_mark_type(self, item_type, item_id):
        """获取原图标记类型"""
        return self.manage_original_mark(item_type, item_id, "get_type")

    def should_use_original_mode(self, image_id):
        """判断图片是否应该使用原图模式"""
        # 检查图片本身是否有标记
        if self.check_original_mark("image", image_id):
            return True

        # 检查图片所在文件夹是否有标记
        result = self.main_app.safe_db_execute(
            "SELECT folder_id FROM images WHERE id = ?", (image_id,), fetch="one"
        )
        if result and result[0]:
            return self.check_original_mark("folder", result[0])

        return False

    def find_similar_images(self, image_id):
        """查找与当前图片名称相似的其他图片"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 首先获取当前图片信息
                cursor = conn.execute(
                    """
                    SELECT name, path, folder_id FROM images WHERE id = ?
                """,
                    (image_id,),
                )
                result = cursor.fetchone()
                if not result:
                    return

                current_name, current_path, folder_id = result

                # 提取基本名称（去除页码等后缀）
                base_name = self.extract_base_name(current_name)

                # 查找相似名称的图片
                if base_name:
                    # 获取同一文件夹中的所有图片
                    cursor = conn.execute(
                        """
                        SELECT id, name, path FROM images
                        WHERE folder_id = ?
                        ORDER BY order_index
                    """,
                        (folder_id,),
                    )

                    all_images = cursor.fetchall()
                    similar_images = []

                    # 筛选出名称相似的图片 - 使用更严格的匹配
                    for img_id, img_name, img_path in all_images:
                        if self.is_same_song_series(current_name, img_name):
                            similar_images.append((img_id, img_name, img_path))

                    if similar_images:
                        self.similar_images = similar_images

                        # 找到当前图片在相似图片列表中的索引
                        for i, (img_id, _, _) in enumerate(similar_images):
                            if img_id == image_id:
                                self.current_similar_index = i
                                break

                        # print(f"找到 {len(similar_images)} 张相似图片")
                        return True

                # 如果没有找到相似图片，清空列表
                self.similar_images = []
                self.current_similar_index = 0
                return False

        except Exception as e:
            print(f"查找相似图片失败: {e}")
            self.similar_images = []
            self.current_similar_index = 0
            return False

    def extract_base_name(self, name):
        """提取图片名称的基本部分（去除数字后缀）"""
        # 处理类似 "第0001首 圣哉三一1" 和 "第0001首 圣哉三一2" 的情况
        match = re.match(r"(第\d+首\s+[^\d]+)\d*$", name)
        if match:
            return match.group(1)

        # 处理类似 "001.圣哉三一歌1" 和 "001.圣哉三一歌2" 的情况
        match = re.match(r"(\d+\.[^0-9]+)\d*$", name)
        if match:
            return match.group(1)

        # 如果没有匹配到特定格式，尝试去掉末尾的数字
        match = re.match(r"(.+?)\d+$", name)
        if match:
            return match.group(1)

        # 如果没有数字后缀，返回完整名称
        return name

    def is_same_song_series(self, name1, name2):
        """判断两个图片是否属于同一首歌的不同页"""
        # 处理类似 "第0001首 圣哉三一1" 和 "第0001首 圣哉三一2" 的情况
        pattern1 = r"第(\d+)首\s+([^\d]+)(\d*)$"
        match1 = re.match(pattern1, name1)
        match2 = re.match(pattern1, name2)

        if match1 and match2:
            # 比较歌曲编号和基本名称
            song_num1, base_name1, _ = match1.groups()
            song_num2, base_name2, _ = match2.groups()

            # 必须是相同的歌曲编号
            if song_num1 == song_num2:
                # 基本名称应该相同或非常相似
                return base_name1.strip() == base_name2.strip()

        # 处理类似 "001.圣哉三一歌1" 和 "001.圣哉三一歌2" 的情况
        pattern2 = r"(\d+)\.([^0-9]+)(\d*)$"
        match1 = re.match(pattern2, name1)
        match2 = re.match(pattern2, name2)

        if match1 and match2:
            # 比较歌曲编号和基本名称
            song_num1, base_name1, _ = match1.groups()
            song_num2, base_name2, _ = match2.groups()

            # 必须是相同的歌曲编号
            if song_num1 == song_num2:
                # 基本名称应该相同或非常相似
                return base_name1.strip() == base_name2.strip()

        # 如果没有特定格式，则比较去掉末尾数字后的名称
        base1 = re.sub(r"\d+$", "", name1).strip()
        base2 = re.sub(r"\d+$", "", name2).strip()

        # 只有当基本名称完全相同且不为空时才认为是相似的
        return base1 and base1 == base2

    def find_and_switch_similar_image(self, direction="next"):
        """查找并切换到相似名称的图片"""
        if not self.similar_images:
            return False

        # 记录切换前的图片ID（用于录制）
        from_image_id = None
        if hasattr(self.main_app, "current_image_id"):
            from_image_id = self.main_app.current_image_id

        # 获取当前图片的标记类型来决定切换模式
        current_switch_mode = "loop"  # 默认循环模式
        if hasattr(self.main_app, "current_image_id"):
            # 检查图片本身的标记类型
            mark_type = self.get_original_mark_type(
                "image", self.main_app.current_image_id
            )
            if mark_type:
                current_switch_mode = mark_type
            else:
                # 检查图片所在文件夹的标记类型
                result = self.main_app.safe_db_execute(
                    "SELECT folder_id FROM images WHERE id = ?",
                    (self.main_app.current_image_id,),
                    fetch="one",
                )
                if result and result[0]:
                    folder_mark_type = self.get_original_mark_type("folder", result[0])
                    if folder_mark_type:
                        current_switch_mode = folder_mark_type

        # 计算新的索引
        if current_switch_mode == "loop":
            # 循环模式：到最后一张时回到第一张
            if direction == "next":
                new_index = (self.current_similar_index + 1) % len(self.similar_images)
            else:  # prev
                new_index = (self.current_similar_index - 1) % len(self.similar_images)
        else:
            # 顺序模式：按照顺序切换，到边界时切换到下一个/上一个不同系列的图片
            if direction == "next":
                new_index = self.current_similar_index + 1
                if new_index >= len(self.similar_images):
                    # print(f"相似图片已到最后，切换到下一个不同系列的图片")
                    return self.switch_to_different_image(direction)
            else:  # prev
                new_index = self.current_similar_index - 1
                if new_index < 0:
                    # print(f"相似图片已到开始，切换到上一个不同系列的图片")
                    return self.switch_to_different_image(direction)

        # 如果索引没有变化，说明只有一张图片
        if new_index == self.current_similar_index:
            return False

        # 获取目标图片信息
        target_id, target_name, target_path = self.similar_images[new_index]

        # 更新当前索引
        self.current_similar_index = new_index

        mode_text = "循环" if current_switch_mode == "loop" else "顺序"
        # print(f"切换到相似图片({mode_text}模式): {target_name} (索引: {new_index+1}/{len(self.similar_images)})")

        # 加载目标图片
        self.main_app.load_image(target_path)

        # 在项目树中选中该图片
        self.main_app.project_tree.selection_set(str(target_id))
        self.main_app.project_tree.see(str(target_id))

        # 强制更新投影窗口
        self.main_app.force_update_projection()

        # 如果正在录制原图模式，记录切换时间
        if (
            hasattr(self.main_app, "time_recorder")
            and self.main_app.time_recorder.is_original_mode_recording
            and from_image_id is not None
        ):
            self.main_app.time_recorder.record_image_switch_timing(
                from_image_id, target_id
            )

            # 检查是否是循环完成（最后一张图切换到第一张图）
            if current_switch_mode == "loop":
                # 获取第一张图的ID
                first_image_id = self.similar_images[0][0]
                # 如果目标图片是第一张图，说明循环完成，自动停止录制
                if target_id == first_image_id:
                    print("检测到循环完成，自动停止录制")
                    # 调用主程序的停止录制方法
                    if hasattr(self.main_app, "toggle_timing_recording"):
                        self.main_app.toggle_timing_recording()

        # 更新按钮状态，确保显示正确的播放和脚本按钮状态
        if hasattr(self.main_app, "update_button_status"):
            self.main_app.root.after(50, self.main_app.update_button_status)

        return True

    def switch_to_different_image(self, direction="next"):
        """切换到下一个/上一个不同系列的图片"""
        if not hasattr(self.main_app, "current_image_id"):
            return False

        try:
            with sqlite3.connect(self.db_path) as conn:
                # 获取当前图片信息
                cursor = conn.execute(
                    """
                    SELECT folder_id, order_index FROM images WHERE id = ?
                """,
                    (self.main_app.current_image_id,),
                )
                result = cursor.fetchone()

                if not result:
                    return False

                folder_id, current_order = result

                # 根据方向查找下一个/上一个图片
                if direction == "next":
                    cursor = conn.execute(
                        """
                        SELECT id, name, path FROM images
                        WHERE folder_id = ? AND order_index > ?
                        ORDER BY order_index ASC
                        LIMIT 1
                    """,
                        (folder_id, current_order),
                    )
                else:  # prev
                    cursor = conn.execute(
                        """
                        SELECT id, name, path FROM images
                        WHERE folder_id = ? AND order_index < ?
                        ORDER BY order_index DESC
                        LIMIT 1
                    """,
                        (folder_id, current_order),
                    )

                result = cursor.fetchone()

                if result:
                    target_id, target_name, target_path = result

                    # print(f"切换到不同系列的图片: {target_name}")

                    # 加载目标图片
                    self.main_app.load_image(target_path)

                    # 在项目树中选中该图片
                    self.main_app.project_tree.selection_set(str(target_id))
                    self.main_app.project_tree.see(str(target_id))

                    # 强制更新投影窗口
                    self.main_app.force_update_projection()

                    return True
                else:
                    direction_text = "下一个" if direction == "next" else "上一个"
                    print(f"没有找到{direction_text}图片")
                    return False

        except Exception as e:
            print(f"切换到不同图片失败: {e}")
            return False
